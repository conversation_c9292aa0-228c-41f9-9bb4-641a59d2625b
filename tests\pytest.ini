[tool:pytest]
# Pytest Configuration
# ====================
# إعدادات pytest

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=backend
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=term-missing
    --cov-fail-under=80
    --html=reports/report.html
    --self-contained-html
    --json-report
    --json-report-file=reports/report.json
    --durations=10
    --maxfail=5

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    performance: Performance tests
    security: Security tests
    slow: Slow running tests
    auth: Authentication tests
    database: Database tests
    api: API tests
    fraud: Fraud detection tests
    risk: Risk assessment tests
    recommendations: Recommendation engine tests
    agents: Agent system tests
    transactions: Transaction tests
    wallets: Wallet tests
    notifications: Notification tests
    admin: Admin panel tests
    mobile: Mobile app tests
    web: Web interface tests
    redis: Redis cache tests
    elasticsearch: Elasticsearch tests
    mongodb: MongoDB tests
    postgresql: PostgreSQL tests
    email: Email service tests
    sms: SMS service tests
    external: External API tests
    mock: Tests using mocks
    real: Tests using real services

# Async settings
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:asyncio
    ignore::RuntimeWarning:asyncio

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# -n auto uses all available CPU cores
# -n 4 uses 4 processes
# Uncomment the line below to enable parallel execution
# addopts = --numprocesses=auto

# Coverage settings
[coverage:run]
source = backend
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */migrations/*
    */alembic/*
    */node_modules/*
    */static/*
    */media/*
    */locale/*
    */docs/*
    */scripts/*
    */deploy/*
    */docker/*
    */kubernetes/*
    */terraform/*
    */ansible/*
    */vagrant/*
    */build/*
    */dist/*
    */htmlcov/*
    */coverage/*
    */reports/*
    */logs/*
    */tmp/*
    */temp/*
    */.git/*
    */.pytest_cache/*
    */.coverage
    */conftest.py
    */setup.py
    */manage.py
    */wsgi.py
    */asgi.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[coverage:html]
directory = htmlcov
title = WS Transfir Test Coverage Report

[coverage:xml]
output = coverage.xml
