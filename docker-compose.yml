version: '3.8'

services:
  # قواعد البيانات
  postgres:
    image: postgres:15-alpine
    container_name: ws-postgres
    environment:
      POSTGRES_DB: ws_transfir
      POSTGRES_USER: ws_user
      POSTGRES_PASSWORD: ws_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - ws-network

  redis:
    image: redis:7-alpine
    container_name: ws-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ws-network

  mongodb:
    image: mongo:7
    container_name: ws-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ws_user
      MONGO_INITDB_ROOT_PASSWORD: ws_password
      MONGO_INITDB_DATABASE: ws_transfir_logs
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - ws-network

  # الخدمات الخلفية
  api-gateway:
    build:
      context: ./backend/api-gateway
      dockerfile: Dockerfile
    container_name: ws-api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
      - MONGODB_URL=************************************************************
    depends_on:
      - postgres
      - redis
      - mongodb
    networks:
      - ws-network

  auth-service:
    build:
      context: ./backend/auth-service
      dockerfile: Dockerfile
    container_name: ws-auth-service
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
    depends_on:
      - postgres
      - redis
    networks:
      - ws-network

  user-service:
    build:
      context: ./backend/user-service
      dockerfile: Dockerfile
    container_name: ws-user-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - ws-network

  transfer-service:
    build:
      context: ./backend/transfer-service
      dockerfile: Dockerfile
    container_name: ws-transfer-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - ws-network

  wallet-service:
    build:
      context: ./backend/wallet-service
      dockerfile: Dockerfile
    container_name: ws-wallet-service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - POSTGRES_URL=**********************************************/ws_transfir
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - ws-network

  # محرك الذكاء الاصطناعي
  ai-engine:
    build:
      context: ./backend/ai-engine
      dockerfile: Dockerfile
    container_name: ws-ai-engine
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_URL=**********************************************/ws_transfir
      - MONGODB_URL=************************************************************
    depends_on:
      - postgres
      - mongodb
    networks:
      - ws-network

  # التطبيق الأمامي
  web-app:
    build:
      context: ./frontend/web-app
      dockerfile: Dockerfile
    container_name: ws-web-app
    ports:
      - "3100:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3000
    depends_on:
      - api-gateway
    networks:
      - ws-network

volumes:
  postgres_data:
  redis_data:
  mongodb_data:

networks:
  ws-network:
    driver: bridge
