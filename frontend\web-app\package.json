{"name": "ws-transfir-web", "version": "1.0.0", "description": "تطبيق الويب لنظام تحويل الأموال WS Transfir", "private": true, "scripts": {"dev": "next dev -p 3100", "build": "next build", "start": "next start -p 3100", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@next/font": "^14.0.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "swr": "^2.2.4", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "react-dropzone": "^14.2.3", "react-qr-code": "^2.0.12", "qr-scanner": "^1.4.2", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "react-intl": "^6.5.5", "next-themes": "^0.2.1", "react-intersection-observer": "^9.5.3", "react-virtual": "^2.10.4", "react-window": "^1.8.8", "lodash": "^4.17.21", "uuid": "^9.0.1", "js-cookie": "^3.0.5", "crypto-js": "^4.2.0", "socket.io-client": "^4.7.4", "react-use": "^17.4.2", "react-helmet-async": "^2.0.4"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/node": "^20.10.5", "@types/lodash": "^4.14.202", "@types/js-cookie": "^3.0.6", "@types/crypto-js": "^4.2.1", "@types/uuid": "^9.0.7", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/aspect-ratio": "^0.4.2", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.6", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0", "@storybook/react": "^7.6.6", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/testing-library": "^0.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}