"""
Accounting Integration Service
=============================
خدمة التكامل مع أنظمة المحاسبة والضرائب
"""

import asyncio
import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from decimal import Decimal
import json
import uuid
import xml.etree.ElementTree as ET
from xml.dom import minidom

import asyncpg
import aiohttp
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class AccountingSystem(Enum):
    """أنظمة المحاسبة"""
    QUICKBOOKS = "quickbooks"
    XERO = "xero"
    SAGE = "sage"
    SAP = "sap"
    ORACLE = "oracle"
    ZATCA = "zatca"  # هيئة الزكاة والضريبة والجمارك
    GAZT = "gazt"   # الهيئة العامة للزكاة والدخل (سابقاً)


class TransactionType(Enum):
    """أنواع المعاملات المحاسبية"""
    REVENUE = "revenue"
    EXPENSE = "expense"
    ASSET = "asset"
    LIABILITY = "liability"
    EQUITY = "equity"
    TAX = "tax"
    FEE = "fee"
    COMMISSION = "commission"


class TaxType(Enum):
    """أنواع الضرائب"""
    VAT = "vat"           # ضريبة القيمة المضافة
    WITHHOLDING = "withholding"  # ضريبة الاستقطاع
    EXCISE = "excise"     # ضريبة الإنتاج الانتقائية
    ZAKAT = "zakat"       # الزكاة
    INCOME = "income"     # ضريبة الدخل


@dataclass
class AccountingEntry:
    """قيد محاسبي"""
    entry_id: str
    transaction_id: str
    account_code: str
    account_name: str
    debit_amount: Decimal
    credit_amount: Decimal
    description: str
    reference: str
    transaction_date: date
    currency: str = "SAR"
    exchange_rate: Decimal = Decimal('1.0')
    tax_amount: Decimal = Decimal('0.0')
    tax_type: TaxType = None
    metadata: Dict[str, Any] = None


@dataclass
class TaxCalculation:
    """حساب الضريبة"""
    tax_id: str
    base_amount: Decimal
    tax_rate: Decimal
    tax_amount: Decimal
    tax_type: TaxType
    currency: str
    calculation_date: date
    is_inclusive: bool = False
    exemption_reason: str = None


@dataclass
class InvoiceData:
    """بيانات الفاتورة"""
    invoice_id: str
    invoice_number: str
    customer_id: str
    customer_name: str
    customer_vat_number: str
    issue_date: date
    due_date: date
    subtotal: Decimal
    tax_amount: Decimal
    total_amount: Decimal
    currency: str
    line_items: List[Dict[str, Any]]
    payment_terms: str = None
    notes: str = None


class AccountingIntegrationService:
    """خدمة التكامل مع أنظمة المحاسبة"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Chart of accounts mapping
        self.chart_of_accounts = {
            "cash": {"code": "1100", "name": "النقد وما في حكمه", "type": "asset"},
            "accounts_receivable": {"code": "1200", "name": "العملاء", "type": "asset"},
            "fees_receivable": {"code": "1210", "name": "رسوم مستحقة", "type": "asset"},
            "prepaid_expenses": {"code": "1300", "name": "مصروفات مدفوعة مقدماً", "type": "asset"},
            "accounts_payable": {"code": "2100", "name": "الموردون", "type": "liability"},
            "accrued_expenses": {"code": "2200", "name": "مصروفات مستحقة", "type": "liability"},
            "vat_payable": {"code": "2300", "name": "ضريبة القيمة المضافة مستحقة", "type": "liability"},
            "withholding_tax": {"code": "2310", "name": "ضريبة الاستقطاع", "type": "liability"},
            "revenue": {"code": "4100", "name": "إيرادات الخدمات", "type": "revenue"},
            "fee_income": {"code": "4200", "name": "إيرادات الرسوم", "type": "revenue"},
            "commission_income": {"code": "4300", "name": "إيرادات العمولات", "type": "revenue"},
            "operating_expenses": {"code": "5100", "name": "مصروفات التشغيل", "type": "expense"},
            "bank_charges": {"code": "5200", "name": "رسوم بنكية", "type": "expense"},
            "technology_expenses": {"code": "5300", "name": "مصروفات تقنية", "type": "expense"}
        }
        
        # Tax rates (Saudi Arabia)
        self.tax_rates = {
            TaxType.VAT: Decimal('0.15'),        # 15% VAT
            TaxType.WITHHOLDING: Decimal('0.05'), # 5% Withholding tax
            TaxType.EXCISE: Decimal('0.50'),     # 50% Excise tax (tobacco)
            TaxType.ZAKAT: Decimal('0.025'),     # 2.5% Zakat
            TaxType.INCOME: Decimal('0.20')      # 20% Income tax
        }
        
        # Accounting system configurations
        self.system_configs = {
            AccountingSystem.ZATCA: {
                "api_endpoint": "https://api.zatca.gov.sa/v1/",
                "sandbox_endpoint": "https://sandbox-api.zatca.gov.sa/v1/",
                "certificate_path": "/certs/zatca_cert.pem",
                "supported_formats": ["xml", "json"],
                "required_fields": ["vat_number", "invoice_number", "issue_date"]
            },
            AccountingSystem.QUICKBOOKS: {
                "api_endpoint": "https://sandbox-quickbooks.api.intuit.com/v3/",
                "oauth_endpoint": "https://appcenter.intuit.com/connect/oauth2",
                "supported_formats": ["json"],
                "required_fields": ["customer_id", "amount", "currency"]
            }
        }
        
        # Statistics
        self.entries_created = 0
        self.invoices_generated = 0
        self.tax_calculations = 0
        
        # HTTP session
        self.http_session = None
    
    async def initialize(self):
        """تهيئة الخدمة"""
        # Create HTTP session
        self.http_session = aiohttp.ClientSession()
        logger.info("📊 Accounting integration service initialized")
    
    async def shutdown(self):
        """إغلاق الخدمة"""
        if self.http_session:
            await self.http_session.close()
        logger.info("📊 Accounting integration service shutdown")
    
    async def create_accounting_entry(
        self, 
        transaction_id: str,
        transaction_type: TransactionType,
        amount: Decimal,
        description: str,
        reference: str = None,
        currency: str = "SAR"
    ) -> List[AccountingEntry]:
        """إنشاء قيد محاسبي"""
        try:
            logger.info(f"📊 Creating accounting entry for transaction: {transaction_id}")
            
            entries = []
            entry_date = date.today()
            
            # Generate entry based on transaction type
            if transaction_type == TransactionType.REVENUE:
                entries = await self._create_revenue_entries(
                    transaction_id, amount, description, reference, entry_date, currency
                )
            elif transaction_type == TransactionType.EXPENSE:
                entries = await self._create_expense_entries(
                    transaction_id, amount, description, reference, entry_date, currency
                )
            elif transaction_type == TransactionType.FEE:
                entries = await self._create_fee_entries(
                    transaction_id, amount, description, reference, entry_date, currency
                )
            elif transaction_type == TransactionType.COMMISSION:
                entries = await self._create_commission_entries(
                    transaction_id, amount, description, reference, entry_date, currency
                )
            
            # Store entries in database
            for entry in entries:
                await self._store_accounting_entry(entry)
            
            # Update statistics
            self.entries_created += len(entries)
            
            logger.info(f"✅ Created {len(entries)} accounting entries for transaction: {transaction_id}")
            return entries
            
        except Exception as e:
            logger.error(f"❌ Failed to create accounting entry: {e}")
            return []
    
    async def calculate_tax(
        self, 
        base_amount: Decimal,
        tax_type: TaxType,
        currency: str = "SAR",
        is_inclusive: bool = False,
        exemption_reason: str = None
    ) -> TaxCalculation:
        """حساب الضريبة"""
        try:
            logger.info(f"💰 Calculating {tax_type.value} tax for amount: {base_amount}")
            
            tax_id = f"tax_{uuid.uuid4().hex[:12]}"
            
            # Check for exemptions
            if exemption_reason:
                return TaxCalculation(
                    tax_id=tax_id,
                    base_amount=base_amount,
                    tax_rate=Decimal('0.0'),
                    tax_amount=Decimal('0.0'),
                    tax_type=tax_type,
                    currency=currency,
                    calculation_date=date.today(),
                    is_inclusive=is_inclusive,
                    exemption_reason=exemption_reason
                )
            
            # Get tax rate
            tax_rate = self.tax_rates.get(tax_type, Decimal('0.0'))
            
            # Calculate tax amount
            if is_inclusive:
                # Tax is included in the base amount
                tax_amount = base_amount * tax_rate / (Decimal('1.0') + tax_rate)
            else:
                # Tax is additional to the base amount
                tax_amount = base_amount * tax_rate
            
            # Round to 2 decimal places
            tax_amount = tax_amount.quantize(Decimal('0.01'))
            
            calculation = TaxCalculation(
                tax_id=tax_id,
                base_amount=base_amount,
                tax_rate=tax_rate,
                tax_amount=tax_amount,
                tax_type=tax_type,
                currency=currency,
                calculation_date=date.today(),
                is_inclusive=is_inclusive,
                exemption_reason=exemption_reason
            )
            
            # Store tax calculation
            await self._store_tax_calculation(calculation)
            
            # Update statistics
            self.tax_calculations += 1
            
            logger.info(f"✅ Tax calculated: {tax_amount} {currency}")
            return calculation
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate tax: {e}")
            raise
    
    async def generate_invoice(
        self, 
        customer_id: str,
        line_items: List[Dict[str, Any]],
        payment_terms: str = "Net 30",
        notes: str = None
    ) -> InvoiceData:
        """إنشاء فاتورة"""
        try:
            logger.info(f"🧾 Generating invoice for customer: {customer_id}")
            
            # Generate invoice ID and number
            invoice_id = f"inv_{uuid.uuid4().hex[:12]}"
            invoice_number = await self._generate_invoice_number()
            
            # Get customer information
            customer_info = await self._get_customer_info(customer_id)
            
            # Calculate totals
            subtotal = Decimal('0.0')
            total_tax = Decimal('0.0')
            
            for item in line_items:
                item_amount = Decimal(str(item.get('amount', 0)))
                subtotal += item_amount
                
                # Calculate VAT for each line item
                if item.get('taxable', True):
                    vat_calc = await self.calculate_tax(
                        item_amount, 
                        TaxType.VAT, 
                        is_inclusive=False
                    )
                    total_tax += vat_calc.tax_amount
                    item['tax_amount'] = float(vat_calc.tax_amount)
                else:
                    item['tax_amount'] = 0.0
            
            total_amount = subtotal + total_tax
            
            # Create invoice data
            invoice = InvoiceData(
                invoice_id=invoice_id,
                invoice_number=invoice_number,
                customer_id=customer_id,
                customer_name=customer_info.get('name', 'Unknown Customer'),
                customer_vat_number=customer_info.get('vat_number', ''),
                issue_date=date.today(),
                due_date=date.today() + timedelta(days=30),
                subtotal=subtotal,
                tax_amount=total_tax,
                total_amount=total_amount,
                currency="SAR",
                line_items=line_items,
                payment_terms=payment_terms,
                notes=notes
            )
            
            # Store invoice
            await self._store_invoice(invoice)
            
            # Create accounting entries for the invoice
            await self.create_accounting_entry(
                invoice_id,
                TransactionType.REVENUE,
                total_amount,
                f"Invoice {invoice_number}",
                invoice_number
            )
            
            # Update statistics
            self.invoices_generated += 1
            
            logger.info(f"✅ Invoice generated: {invoice_number} - Total: {total_amount} SAR")
            return invoice
            
        except Exception as e:
            logger.error(f"❌ Failed to generate invoice: {e}")
            raise
    
    async def submit_to_zatca(self, invoice: InvoiceData) -> Dict[str, Any]:
        """تقديم الفاتورة لهيئة الزكاة والضريبة والجمارك"""
        try:
            logger.info(f"🏛️ Submitting invoice to ZATCA: {invoice.invoice_number}")
            
            # Generate ZATCA XML format
            zatca_xml = await self._generate_zatca_xml(invoice)
            
            # Sign the invoice (simplified - in production use proper digital signature)
            signed_xml = await self._sign_zatca_invoice(zatca_xml)
            
            # Submit to ZATCA API (simplified implementation)
            config = self.system_configs[AccountingSystem.ZATCA]
            
            headers = {
                "Content-Type": "application/xml",
                "Authorization": "Bearer your_zatca_token",
                "Accept": "application/json"
            }
            
            # In production, use actual ZATCA API
            # For now, simulate successful submission
            await asyncio.sleep(0.5)  # Simulate API call
            
            response_data = {
                "status": "accepted",
                "invoice_hash": hashlib.sha256(signed_xml.encode()).hexdigest(),
                "qr_code": await self._generate_qr_code(invoice),
                "zatca_reference": f"ZATCA{datetime.now().strftime('%Y%m%d')}{invoice.invoice_id[-6:]}",
                "submission_time": datetime.now().isoformat()
            }
            
            # Store ZATCA submission record
            await self._store_zatca_submission(invoice.invoice_id, response_data)
            
            logger.info(f"✅ Invoice submitted to ZATCA: {response_data['zatca_reference']}")
            return response_data
            
        except Exception as e:
            logger.error(f"❌ Failed to submit to ZATCA: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def sync_with_accounting_system(
        self, 
        system: AccountingSystem,
        start_date: date = None,
        end_date: date = None
    ) -> Dict[str, Any]:
        """مزامنة مع نظام المحاسبة"""
        try:
            logger.info(f"🔄 Syncing with {system.value}")
            
            if not start_date:
                start_date = date.today() - timedelta(days=7)
            
            if not end_date:
                end_date = date.today()
            
            # Get entries to sync
            entries = await self._get_entries_for_sync(start_date, end_date)
            
            synced_count = 0
            failed_count = 0
            
            for entry in entries:
                try:
                    if system == AccountingSystem.QUICKBOOKS:
                        result = await self._sync_to_quickbooks(entry)
                    elif system == AccountingSystem.XERO:
                        result = await self._sync_to_xero(entry)
                    elif system == AccountingSystem.ZATCA:
                        result = await self._sync_to_zatca(entry)
                    else:
                        result = False
                    
                    if result:
                        synced_count += 1
                        await self._mark_entry_synced(entry['entry_id'], system)
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"❌ Failed to sync entry {entry['entry_id']}: {e}")
                    failed_count += 1
                    continue
            
            sync_result = {
                "system": system.value,
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "total_entries": len(entries),
                "synced_count": synced_count,
                "failed_count": failed_count,
                "success_rate": (synced_count / max(len(entries), 1)) * 100
            }
            
            logger.info(f"✅ Sync completed: {synced_count}/{len(entries)} entries synced")
            return sync_result
            
        except Exception as e:
            logger.error(f"❌ Failed to sync with accounting system: {e}")
            return {"status": "failed", "error": str(e)}
    
    async def get_financial_summary(
        self, 
        start_date: date = None,
        end_date: date = None
    ) -> Dict[str, Any]:
        """الحصول على الملخص المالي"""
        try:
            if not start_date:
                start_date = date.today().replace(day=1)  # First day of current month
            
            if not end_date:
                end_date = date.today()
            
            async with self.db_connection.get_connection() as conn:
                # Revenue summary
                revenue_query = """
                    SELECT 
                        SUM(credit_amount) as total_revenue,
                        COUNT(*) as revenue_transactions
                    FROM accounting_entries 
                    WHERE account_code LIKE '4%' 
                    AND transaction_date BETWEEN $1 AND $2
                """
                
                revenue_data = await conn.fetchrow(revenue_query, start_date, end_date)
                
                # Expense summary
                expense_query = """
                    SELECT 
                        SUM(debit_amount) as total_expenses,
                        COUNT(*) as expense_transactions
                    FROM accounting_entries 
                    WHERE account_code LIKE '5%' 
                    AND transaction_date BETWEEN $1 AND $2
                """
                
                expense_data = await conn.fetchrow(expense_query, start_date, end_date)
                
                # Tax summary
                tax_query = """
                    SELECT 
                        tax_type,
                        SUM(tax_amount) as total_tax,
                        COUNT(*) as tax_calculations
                    FROM tax_calculations 
                    WHERE calculation_date BETWEEN $1 AND $2
                    GROUP BY tax_type
                """
                
                tax_data = await conn.fetch(tax_query, start_date, end_date)
                
                # Invoice summary
                invoice_query = """
                    SELECT 
                        COUNT(*) as total_invoices,
                        SUM(total_amount) as total_invoiced,
                        SUM(tax_amount) as total_tax_invoiced
                    FROM invoices 
                    WHERE issue_date BETWEEN $1 AND $2
                """
                
                invoice_data = await conn.fetchrow(invoice_query, start_date, end_date)
                
                # Calculate net income
                total_revenue = revenue_data['total_revenue'] or Decimal('0.0')
                total_expenses = expense_data['total_expenses'] or Decimal('0.0')
                net_income = total_revenue - total_expenses
                
                return {
                    "period": {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat()
                    },
                    "revenue": {
                        "total_amount": float(total_revenue),
                        "transaction_count": revenue_data['revenue_transactions'] or 0
                    },
                    "expenses": {
                        "total_amount": float(total_expenses),
                        "transaction_count": expense_data['expense_transactions'] or 0
                    },
                    "net_income": float(net_income),
                    "tax_summary": [
                        {
                            "tax_type": row['tax_type'],
                            "total_amount": float(row['total_tax']),
                            "calculation_count": row['tax_calculations']
                        }
                        for row in tax_data
                    ],
                    "invoices": {
                        "total_count": invoice_data['total_invoices'] or 0,
                        "total_amount": float(invoice_data['total_invoiced'] or 0),
                        "total_tax": float(invoice_data['total_tax_invoiced'] or 0)
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get financial summary: {e}")
            return {}
    
    # Helper methods
    async def _create_revenue_entries(
        self, 
        transaction_id: str, 
        amount: Decimal, 
        description: str,
        reference: str,
        entry_date: date,
        currency: str
    ) -> List[AccountingEntry]:
        """إنشاء قيود الإيرادات"""
        entries = []
        
        # Calculate VAT
        vat_calc = await self.calculate_tax(amount, TaxType.VAT, currency, is_inclusive=True)
        net_amount = amount - vat_calc.tax_amount
        
        # Debit: Accounts Receivable
        entries.append(AccountingEntry(
            entry_id=f"entry_{uuid.uuid4().hex[:12]}",
            transaction_id=transaction_id,
            account_code=self.chart_of_accounts["accounts_receivable"]["code"],
            account_name=self.chart_of_accounts["accounts_receivable"]["name"],
            debit_amount=amount,
            credit_amount=Decimal('0.0'),
            description=description,
            reference=reference or transaction_id,
            transaction_date=entry_date,
            currency=currency
        ))
        
        # Credit: Revenue
        entries.append(AccountingEntry(
            entry_id=f"entry_{uuid.uuid4().hex[:12]}",
            transaction_id=transaction_id,
            account_code=self.chart_of_accounts["revenue"]["code"],
            account_name=self.chart_of_accounts["revenue"]["name"],
            debit_amount=Decimal('0.0'),
            credit_amount=net_amount,
            description=description,
            reference=reference or transaction_id,
            transaction_date=entry_date,
            currency=currency
        ))
        
        # Credit: VAT Payable (if applicable)
        if vat_calc.tax_amount > 0:
            entries.append(AccountingEntry(
                entry_id=f"entry_{uuid.uuid4().hex[:12]}",
                transaction_id=transaction_id,
                account_code=self.chart_of_accounts["vat_payable"]["code"],
                account_name=self.chart_of_accounts["vat_payable"]["name"],
                debit_amount=Decimal('0.0'),
                credit_amount=vat_calc.tax_amount,
                description=f"VAT on {description}",
                reference=reference or transaction_id,
                transaction_date=entry_date,
                currency=currency,
                tax_amount=vat_calc.tax_amount,
                tax_type=TaxType.VAT
            ))
        
        return entries
    
    async def _create_expense_entries(
        self, 
        transaction_id: str, 
        amount: Decimal, 
        description: str,
        reference: str,
        entry_date: date,
        currency: str
    ) -> List[AccountingEntry]:
        """إنشاء قيود المصروفات"""
        entries = []
        
        # Debit: Operating Expenses
        entries.append(AccountingEntry(
            entry_id=f"entry_{uuid.uuid4().hex[:12]}",
            transaction_id=transaction_id,
            account_code=self.chart_of_accounts["operating_expenses"]["code"],
            account_name=self.chart_of_accounts["operating_expenses"]["name"],
            debit_amount=amount,
            credit_amount=Decimal('0.0'),
            description=description,
            reference=reference or transaction_id,
            transaction_date=entry_date,
            currency=currency
        ))
        
        # Credit: Cash
        entries.append(AccountingEntry(
            entry_id=f"entry_{uuid.uuid4().hex[:12]}",
            transaction_id=transaction_id,
            account_code=self.chart_of_accounts["cash"]["code"],
            account_name=self.chart_of_accounts["cash"]["name"],
            debit_amount=Decimal('0.0'),
            credit_amount=amount,
            description=description,
            reference=reference or transaction_id,
            transaction_date=entry_date,
            currency=currency
        ))
        
        return entries
    
    async def _create_fee_entries(
        self, 
        transaction_id: str, 
        amount: Decimal, 
        description: str,
        reference: str,
        entry_date: date,
        currency: str
    ) -> List[AccountingEntry]:
        """إنشاء قيود الرسوم"""
        entries = []
        
        # Debit: Cash
        entries.append(AccountingEntry(
            entry_id=f"entry_{uuid.uuid4().hex[:12]}",
            transaction_id=transaction_id,
            account_code=self.chart_of_accounts["cash"]["code"],
            account_name=self.chart_of_accounts["cash"]["name"],
            debit_amount=amount,
            credit_amount=Decimal('0.0'),
            description=description,
            reference=reference or transaction_id,
            transaction_date=entry_date,
            currency=currency
        ))
        
        # Credit: Fee Income
        entries.append(AccountingEntry(
            entry_id=f"entry_{uuid.uuid4().hex[:12]}",
            transaction_id=transaction_id,
            account_code=self.chart_of_accounts["fee_income"]["code"],
            account_name=self.chart_of_accounts["fee_income"]["name"],
            debit_amount=Decimal('0.0'),
            credit_amount=amount,
            description=description,
            reference=reference or transaction_id,
            transaction_date=entry_date,
            currency=currency
        ))
        
        return entries
    
    async def _create_commission_entries(
        self, 
        transaction_id: str, 
        amount: Decimal, 
        description: str,
        reference: str,
        entry_date: date,
        currency: str
    ) -> List[AccountingEntry]:
        """إنشاء قيود العمولات"""
        entries = []
        
        # Debit: Cash
        entries.append(AccountingEntry(
            entry_id=f"entry_{uuid.uuid4().hex[:12]}",
            transaction_id=transaction_id,
            account_code=self.chart_of_accounts["cash"]["code"],
            account_name=self.chart_of_accounts["cash"]["name"],
            debit_amount=amount,
            credit_amount=Decimal('0.0'),
            description=description,
            reference=reference or transaction_id,
            transaction_date=entry_date,
            currency=currency
        ))
        
        # Credit: Commission Income
        entries.append(AccountingEntry(
            entry_id=f"entry_{uuid.uuid4().hex[:12]}",
            transaction_id=transaction_id,
            account_code=self.chart_of_accounts["commission_income"]["code"],
            account_name=self.chart_of_accounts["commission_income"]["name"],
            debit_amount=Decimal('0.0'),
            credit_amount=amount,
            description=description,
            reference=reference or transaction_id,
            transaction_date=entry_date,
            currency=currency
        ))
        
        return entries
    
    async def _store_accounting_entry(self, entry: AccountingEntry):
        """حفظ القيد المحاسبي"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO accounting_entries (
                        entry_id, transaction_id, account_code, account_name,
                        debit_amount, credit_amount, description, reference,
                        transaction_date, currency, exchange_rate, tax_amount, tax_type, metadata
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                """
                
                await conn.execute(
                    query,
                    entry.entry_id,
                    entry.transaction_id,
                    entry.account_code,
                    entry.account_name,
                    entry.debit_amount,
                    entry.credit_amount,
                    entry.description,
                    entry.reference,
                    entry.transaction_date,
                    entry.currency,
                    entry.exchange_rate,
                    entry.tax_amount,
                    entry.tax_type.value if entry.tax_type else None,
                    json.dumps(entry.metadata or {})
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store accounting entry: {e}")
            raise
    
    async def _store_tax_calculation(self, calculation: TaxCalculation):
        """حفظ حساب الضريبة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO tax_calculations (
                        tax_id, base_amount, tax_rate, tax_amount, tax_type,
                        currency, calculation_date, is_inclusive, exemption_reason
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """
                
                await conn.execute(
                    query,
                    calculation.tax_id,
                    calculation.base_amount,
                    calculation.tax_rate,
                    calculation.tax_amount,
                    calculation.tax_type.value,
                    calculation.currency,
                    calculation.calculation_date,
                    calculation.is_inclusive,
                    calculation.exemption_reason
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store tax calculation: {e}")
            raise
    
    async def _generate_invoice_number(self) -> str:
        """إنشاء رقم الفاتورة"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Get next invoice number
                query = """
                    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 5) AS INTEGER)), 0) + 1 as next_number
                    FROM invoices 
                    WHERE invoice_number LIKE 'INV-%'
                """
                
                row = await conn.fetchrow(query)
                next_number = row['next_number']
                
                return f"INV-{next_number:06d}"
                
        except Exception as e:
            logger.error(f"❌ Failed to generate invoice number: {e}")
            return f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    async def _get_customer_info(self, customer_id: str) -> Dict[str, Any]:
        """الحصول على معلومات العميل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT name, email, vat_number, address
                    FROM users 
                    WHERE id = $1
                """
                
                row = await conn.fetchrow(query, customer_id)
                
                if row:
                    return {
                        "name": row['name'],
                        "email": row['email'],
                        "vat_number": row.get('vat_number', ''),
                        "address": row.get('address', '')
                    }
                
                return {"name": "Unknown Customer", "email": "", "vat_number": "", "address": ""}
                
        except Exception as e:
            logger.error(f"❌ Failed to get customer info: {e}")
            return {"name": "Unknown Customer", "email": "", "vat_number": "", "address": ""}
    
    async def _store_invoice(self, invoice: InvoiceData):
        """حفظ الفاتورة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO invoices (
                        invoice_id, invoice_number, customer_id, customer_name,
                        customer_vat_number, issue_date, due_date, subtotal,
                        tax_amount, total_amount, currency, line_items,
                        payment_terms, notes
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                """
                
                await conn.execute(
                    query,
                    invoice.invoice_id,
                    invoice.invoice_number,
                    invoice.customer_id,
                    invoice.customer_name,
                    invoice.customer_vat_number,
                    invoice.issue_date,
                    invoice.due_date,
                    invoice.subtotal,
                    invoice.tax_amount,
                    invoice.total_amount,
                    invoice.currency,
                    json.dumps(invoice.line_items),
                    invoice.payment_terms,
                    invoice.notes
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store invoice: {e}")
            raise
    
    async def _generate_zatca_xml(self, invoice: InvoiceData) -> str:
        """إنشاء XML للهيئة"""
        # Simplified ZATCA XML generation
        # In production, use proper UBL 2.1 format
        
        root = ET.Element("Invoice")
        root.set("xmlns", "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2")
        
        # Invoice header
        ET.SubElement(root, "ID").text = invoice.invoice_number
        ET.SubElement(root, "IssueDate").text = invoice.issue_date.isoformat()
        ET.SubElement(root, "InvoiceTypeCode").text = "388"  # Tax invoice
        
        # Supplier party
        supplier_party = ET.SubElement(root, "AccountingSupplierParty")
        party = ET.SubElement(supplier_party, "Party")
        ET.SubElement(party, "PartyName").text = "WalletSystem"
        
        # Customer party
        customer_party = ET.SubElement(root, "AccountingCustomerParty")
        party = ET.SubElement(customer_party, "Party")
        ET.SubElement(party, "PartyName").text = invoice.customer_name
        
        # Tax total
        tax_total = ET.SubElement(root, "TaxTotal")
        ET.SubElement(tax_total, "TaxAmount", currencyID=invoice.currency).text = str(invoice.tax_amount)
        
        # Legal monetary total
        monetary_total = ET.SubElement(root, "LegalMonetaryTotal")
        ET.SubElement(monetary_total, "LineExtensionAmount", currencyID=invoice.currency).text = str(invoice.subtotal)
        ET.SubElement(monetary_total, "TaxExclusiveAmount", currencyID=invoice.currency).text = str(invoice.subtotal)
        ET.SubElement(monetary_total, "TaxInclusiveAmount", currencyID=invoice.currency).text = str(invoice.total_amount)
        ET.SubElement(monetary_total, "PayableAmount", currencyID=invoice.currency).text = str(invoice.total_amount)
        
        # Convert to string
        return ET.tostring(root, encoding='unicode')
    
    async def _sign_zatca_invoice(self, xml_content: str) -> str:
        """توقيع فاتورة الهيئة"""
        # Simplified digital signature
        # In production, use proper XML digital signature
        import hashlib
        
        signature = hashlib.sha256(xml_content.encode()).hexdigest()
        
        # Add signature to XML (simplified)
        signed_xml = xml_content.replace(
            "</Invoice>",
            f"<Signature>{signature}</Signature></Invoice>"
        )
        
        return signed_xml
    
    async def _generate_qr_code(self, invoice: InvoiceData) -> str:
        """إنشاء رمز QR للفاتورة"""
        # Simplified QR code generation
        # In production, use proper QR code library and ZATCA format
        
        qr_data = f"Invoice:{invoice.invoice_number}|Amount:{invoice.total_amount}|Date:{invoice.issue_date}"
        
        # Return base64 encoded QR data (simplified)
        import base64
        return base64.b64encode(qr_data.encode()).decode()
    
    async def _store_zatca_submission(self, invoice_id: str, response_data: Dict[str, Any]):
        """حفظ سجل تقديم الهيئة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO zatca_submissions (
                        invoice_id, status, invoice_hash, qr_code,
                        zatca_reference, submission_time, response_data
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """
                
                await conn.execute(
                    query,
                    invoice_id,
                    response_data['status'],
                    response_data['invoice_hash'],
                    response_data['qr_code'],
                    response_data['zatca_reference'],
                    datetime.fromisoformat(response_data['submission_time']),
                    json.dumps(response_data)
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store ZATCA submission: {e}")
    
    async def _get_entries_for_sync(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """الحصول على القيود للمزامنة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM accounting_entries 
                    WHERE transaction_date BETWEEN $1 AND $2
                    AND synced_systems IS NULL OR NOT synced_systems ? 'quickbooks'
                    ORDER BY transaction_date, entry_id
                """
                
                rows = await conn.fetch(query, start_date, end_date)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Failed to get entries for sync: {e}")
            return []
    
    async def _sync_to_quickbooks(self, entry: Dict[str, Any]) -> bool:
        """مزامنة مع QuickBooks"""
        # Simplified QuickBooks sync
        # In production, use QuickBooks API
        await asyncio.sleep(0.1)  # Simulate API call
        return True
    
    async def _sync_to_xero(self, entry: Dict[str, Any]) -> bool:
        """مزامنة مع Xero"""
        # Simplified Xero sync
        # In production, use Xero API
        await asyncio.sleep(0.1)  # Simulate API call
        return True
    
    async def _sync_to_zatca(self, entry: Dict[str, Any]) -> bool:
        """مزامنة مع الهيئة"""
        # Simplified ZATCA sync
        # In production, use ZATCA API
        await asyncio.sleep(0.1)  # Simulate API call
        return True
    
    async def _mark_entry_synced(self, entry_id: str, system: AccountingSystem):
        """تحديد القيد كمتزامن"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE accounting_entries 
                    SET synced_systems = COALESCE(synced_systems, '{}'::jsonb) || $1::jsonb
                    WHERE entry_id = $2
                """
                
                await conn.execute(
                    query,
                    json.dumps({system.value: datetime.now().isoformat()}),
                    entry_id
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to mark entry as synced: {e}")
    
    async def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمة"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Count total entries
                entries_query = "SELECT COUNT(*) FROM accounting_entries"
                entries_count = await conn.fetchval(entries_query)
                
                # Count total invoices
                invoices_query = "SELECT COUNT(*) FROM invoices"
                invoices_count = await conn.fetchval(invoices_query)
                
                return {
                    "entries_created": self.entries_created,
                    "invoices_generated": self.invoices_generated,
                    "tax_calculations": self.tax_calculations,
                    "total_entries_in_db": entries_count,
                    "total_invoices_in_db": invoices_count,
                    "supported_systems": len(self.system_configs),
                    "chart_of_accounts_size": len(self.chart_of_accounts),
                    "supported_tax_types": len(self.tax_rates)
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get service statistics: {e}")
            return {
                "entries_created": self.entries_created,
                "invoices_generated": self.invoices_generated,
                "tax_calculations": self.tax_calculations,
                "total_entries_in_db": 0,
                "total_invoices_in_db": 0,
                "supported_systems": len(self.system_configs),
                "chart_of_accounts_size": len(self.chart_of_accounts),
                "supported_tax_types": len(self.tax_rates)
            }
