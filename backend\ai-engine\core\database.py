"""
Database Manager for AI Engine
==============================
مدير قاعدة البيانات لمحرك الذكاء الاصطناعي
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

import asyncpg
import motor.motor_asyncio
from elasticsearch import AsyncElasticsearch

from core.config import settings

logger = logging.getLogger(__name__)


class DatabaseManager:
    """مدير قواعد البيانات"""
    
    def __init__(self):
        # Database connections
        self.postgres_pool = None
        self.mongodb_client = None
        self.mongodb_db = None
        self.elasticsearch_client = None
        
        # Connection status
        self.postgres_connected = False
        self.mongodb_connected = False
        self.elasticsearch_connected = False
    
    async def connect(self):
        """الاتصال بقواعد البيانات"""
        try:
            logger.info("🔌 Connecting to databases...")
            
            # Connect to PostgreSQL
            await self._connect_postgres()
            
            # Connect to MongoDB
            await self._connect_mongodb()
            
            # Connect to Elasticsearch
            await self._connect_elasticsearch()
            
            logger.info("✅ All databases connected successfully")
            
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            raise
    
    async def _connect_postgres(self):
        """الاتصال بـ PostgreSQL"""
        try:
            self.postgres_pool = await asyncpg.create_pool(
                settings.POSTGRES_URL,
                min_size=5,
                max_size=settings.DB_POOL_SIZE,
                command_timeout=settings.DB_POOL_TIMEOUT
            )
            self.postgres_connected = True
            logger.info("✅ PostgreSQL connected")
            
        except Exception as e:
            logger.error(f"❌ PostgreSQL connection failed: {e}")
            self.postgres_connected = False
    
    async def _connect_mongodb(self):
        """الاتصال بـ MongoDB"""
        try:
            self.mongodb_client = motor.motor_asyncio.AsyncIOMotorClient(
                settings.MONGODB_URL,
                serverSelectionTimeoutMS=5000
            )
            
            # Test connection
            await self.mongodb_client.admin.command('ping')
            
            # Get database
            db_name = settings.MONGODB_URL.split('/')[-1]
            self.mongodb_db = self.mongodb_client[db_name]
            
            self.mongodb_connected = True
            logger.info("✅ MongoDB connected")
            
        except Exception as e:
            logger.error(f"❌ MongoDB connection failed: {e}")
            self.mongodb_connected = False
    
    async def _connect_elasticsearch(self):
        """الاتصال بـ Elasticsearch"""
        try:
            self.elasticsearch_client = AsyncElasticsearch(
                [settings.ELASTICSEARCH_URL],
                timeout=30,
                max_retries=3,
                retry_on_timeout=True
            )
            
            # Test connection
            info = await self.elasticsearch_client.info()
            logger.info(f"✅ Elasticsearch connected: {info['version']['number']}")
            
            self.elasticsearch_connected = True
            
        except Exception as e:
            logger.error(f"❌ Elasticsearch connection failed: {e}")
            self.elasticsearch_connected = False
    
    async def health_check(self) -> bool:
        """فحص صحة الاتصالات"""
        try:
            # Check PostgreSQL
            if self.postgres_pool:
                async with self.postgres_pool.acquire() as conn:
                    await conn.fetchval('SELECT 1')
            
            # Check MongoDB
            if self.mongodb_client:
                await self.mongodb_client.admin.command('ping')
            
            # Check Elasticsearch
            if self.elasticsearch_client:
                await self.elasticsearch_client.ping()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Database health check failed: {e}")
            return False
    
    # User Profile Methods
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على ملف المستخدم"""
        try:
            if not self.postgres_connected:
                return None
            
            async with self.postgres_pool.acquire() as conn:
                query = """
                    SELECT u.*, up.* 
                    FROM users u 
                    LEFT JOIN user_profiles up ON u.id = up.user_id 
                    WHERE u.id = $1
                """
                row = await conn.fetchrow(query, user_id)
                
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get user profile: {e}")
            return None
    
    async def get_user_risk_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على ملف مخاطر المستخدم"""
        try:
            if not self.postgres_connected:
                return None
            
            async with self.postgres_pool.acquire() as conn:
                query = """
                    SELECT * FROM user_risk_profiles 
                    WHERE user_id = $1
                """
                row = await conn.fetchrow(query, user_id)
                
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get user risk profile: {e}")
            return None
    
    async def get_user_preferences(self, user_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على تفضيلات المستخدم"""
        try:
            if not self.postgres_connected:
                return None
            
            async with self.postgres_pool.acquire() as conn:
                query = """
                    SELECT * FROM user_preferences 
                    WHERE user_id = $1
                """
                row = await conn.fetchrow(query, user_id)
                
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get user preferences: {e}")
            return None
    
    async def get_user_transactions(
        self,
        user_id: str,
        limit: int = 100,
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """الحصول على معاملات المستخدم"""
        try:
            if not self.postgres_connected:
                return []
            
            async with self.postgres_pool.acquire() as conn:
                query = """
                    SELECT * FROM transactions 
                    WHERE user_id = $1 
                    AND created_at >= $2 
                    ORDER BY created_at DESC 
                    LIMIT $3
                """
                cutoff_date = datetime.now() - timedelta(days=days)
                rows = await conn.fetch(query, user_id, cutoff_date, limit)
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Failed to get user transactions: {e}")
            return []
    
    # Merchant Profile Methods
    async def get_merchant_profile(self, merchant_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على ملف التاجر"""
        try:
            if not self.postgres_connected:
                return None
            
            async with self.postgres_pool.acquire() as conn:
                query = """
                    SELECT * FROM merchants 
                    WHERE id = $1
                """
                row = await conn.fetchrow(query, merchant_id)
                
                if row:
                    return dict(row)
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get merchant profile: {e}")
            return None
    
    # Risk Data Methods
    async def get_country_risk_data(self) -> Dict[str, Any]:
        """الحصول على بيانات مخاطر البلدان"""
        try:
            if not self.postgres_connected:
                return {}
            
            async with self.postgres_pool.acquire() as conn:
                query = """
                    SELECT country_code, risk_score, risk_level 
                    FROM country_risk_data
                """
                rows = await conn.fetch(query)
                
                return {
                    row['country_code']: {
                        'risk_score': row['risk_score'],
                        'risk_level': row['risk_level']
                    }
                    for row in rows
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get country risk data: {e}")
            return {}
    
    async def get_all_user_profiles(self) -> Dict[str, Any]:
        """الحصول على جميع ملفات المستخدمين"""
        try:
            if not self.postgres_connected:
                return {}
            
            async with self.postgres_pool.acquire() as conn:
                query = """
                    SELECT u.id, u.age, u.country, up.account_age_days, 
                           up.kyc_level, up.avg_transaction_amount, up.transaction_count
                    FROM users u 
                    LEFT JOIN user_profiles up ON u.id = up.user_id
                    LIMIT 1000
                """
                rows = await conn.fetch(query)
                
                return {
                    row['id']: {
                        'age': row['age'],
                        'country': row['country'],
                        'account_age_days': row['account_age_days'] or 365,
                        'kyc_level': row['kyc_level'] or 1,
                        'avg_transaction_amount': row['avg_transaction_amount'] or 1000,
                        'transaction_count': row['transaction_count'] or 10
                    }
                    for row in rows
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get all user profiles: {e}")
            return {}
    
    async def get_all_merchant_profiles(self) -> Dict[str, Any]:
        """الحصول على جميع ملفات التجار"""
        try:
            if not self.postgres_connected:
                return {}
            
            async with self.postgres_pool.acquire() as conn:
                query = """
                    SELECT id, risk_category, transaction_volume, 
                           chargeback_rate, reputation_score
                    FROM merchants
                    LIMIT 1000
                """
                rows = await conn.fetch(query)
                
                return {
                    row['id']: {
                        'risk_category': row['risk_category'] or 'medium',
                        'transaction_volume': row['transaction_volume'] or 1000000,
                        'chargeback_rate': row['chargeback_rate'] or 0.01,
                        'reputation_score': row['reputation_score'] or 0.8
                    }
                    for row in rows
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get all merchant profiles: {e}")
            return {}
    
    # Logging Methods
    async def log_prediction(
        self,
        model_type: str,
        user_id: str,
        prediction_data: Dict[str, Any]
    ):
        """تسجيل التنبؤ"""
        try:
            if not self.mongodb_connected:
                return
            
            log_entry = {
                "timestamp": datetime.now(),
                "model_type": model_type,
                "user_id": user_id,
                "prediction_data": prediction_data,
                "service": "ai-engine"
            }
            
            await self.mongodb_db.predictions.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"❌ Failed to log prediction: {e}")
    
    async def log_model_performance(
        self,
        model_name: str,
        metrics: Dict[str, Any]
    ):
        """تسجيل أداء النموذج"""
        try:
            if not self.mongodb_connected:
                return
            
            log_entry = {
                "timestamp": datetime.now(),
                "model_name": model_name,
                "metrics": metrics,
                "service": "ai-engine"
            }
            
            await self.mongodb_db.model_performance.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"❌ Failed to log model performance: {e}")
    
    # Search Methods
    async def search_transactions(
        self,
        query: str,
        filters: Dict[str, Any] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """البحث في المعاملات"""
        try:
            if not self.elasticsearch_connected:
                return []
            
            search_body = {
                "query": {
                    "bool": {
                        "must": [
                            {"multi_match": {
                                "query": query,
                                "fields": ["description", "merchant_name", "category"]
                            }}
                        ]
                    }
                },
                "size": limit
            }
            
            # Add filters
            if filters:
                for key, value in filters.items():
                    search_body["query"]["bool"]["must"].append({
                        "term": {key: value}
                    })
            
            response = await self.elasticsearch_client.search(
                index="transactions",
                body=search_body
            )
            
            return [hit["_source"] for hit in response["hits"]["hits"]]
            
        except Exception as e:
            logger.error(f"❌ Transaction search failed: {e}")
            return []
    
    async def disconnect(self):
        """قطع الاتصال بقواعد البيانات"""
        try:
            logger.info("🔌 Disconnecting from databases...")
            
            # Close PostgreSQL pool
            if self.postgres_pool:
                await self.postgres_pool.close()
                self.postgres_connected = False
            
            # Close MongoDB client
            if self.mongodb_client:
                self.mongodb_client.close()
                self.mongodb_connected = False
            
            # Close Elasticsearch client
            if self.elasticsearch_client:
                await self.elasticsearch_client.close()
                self.elasticsearch_connected = False
            
            logger.info("✅ All databases disconnected")
            
        except Exception as e:
            logger.error(f"❌ Database disconnection failed: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات قواعد البيانات"""
        return {
            "postgres_connected": self.postgres_connected,
            "mongodb_connected": self.mongodb_connected,
            "elasticsearch_connected": self.elasticsearch_connected,
            "postgres_pool_size": self.postgres_pool.get_size() if self.postgres_pool else 0,
            "postgres_pool_free": self.postgres_pool.get_idle_size() if self.postgres_pool else 0
        }
