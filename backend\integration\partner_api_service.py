"""
Partner API Service
==================
خدمة واجهات برمجة التطبيقات للشركاء
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from decimal import Decimal
import json
import uuid
import hashlib
import hmac
import base64
from urllib.parse import urlencode
import jwt

import asyncpg
import aiohttp
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class PartnerType(Enum):
    """أنواع الشركاء"""
    MERCHANT = "merchant"
    PAYMENT_PROCESSOR = "payment_processor"
    BANK = "bank"
    FINTECH = "fintech"
    GOVERNMENT = "government"
    ENTERPRISE = "enterprise"


class APIVersion(Enum):
    """إصدارات API"""
    V1 = "v1"
    V2 = "v2"
    V3 = "v3"


class AuthMethod(Enum):
    """طرق المصادقة"""
    API_KEY = "api_key"
    JWT = "jwt"
    OAUTH2 = "oauth2"
    MUTUAL_TLS = "mutual_tls"


class RateLimit(Enum):
    """حدود المعدل"""
    BASIC = "basic"      # 100 requests/minute
    STANDARD = "standard"  # 500 requests/minute
    PREMIUM = "premium"   # 2000 requests/minute
    ENTERPRISE = "enterprise"  # 10000 requests/minute


@dataclass
class PartnerConfig:
    """إعدادات الشريك"""
    partner_id: str
    partner_name: str
    partner_type: PartnerType
    api_version: APIVersion
    auth_method: AuthMethod
    rate_limit: RateLimit
    api_key: str = None
    secret_key: str = None
    webhook_url: str = None
    allowed_ips: List[str] = None
    is_active: bool = True
    metadata: Dict[str, Any] = None


@dataclass
class APIRequest:
    """طلب API"""
    partner_id: str
    endpoint: str
    method: str
    headers: Dict[str, str]
    body: Dict[str, Any]
    ip_address: str
    user_agent: str
    timestamp: datetime


@dataclass
class APIResponse:
    """استجابة API"""
    status_code: int
    data: Dict[str, Any]
    headers: Dict[str, str]
    processing_time: float
    request_id: str


class PartnerAPIService:
    """خدمة واجهات برمجة التطبيقات للشركاء"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Rate limiting configuration
        self.rate_limits = {
            RateLimit.BASIC: {"requests_per_minute": 100, "burst": 20},
            RateLimit.STANDARD: {"requests_per_minute": 500, "burst": 50},
            RateLimit.PREMIUM: {"requests_per_minute": 2000, "burst": 200},
            RateLimit.ENTERPRISE: {"requests_per_minute": 10000, "burst": 1000}
        }
        
        # API endpoints configuration
        self.api_endpoints = {
            APIVersion.V1: {
                "/payments": ["POST", "GET"],
                "/payments/{id}": ["GET", "PUT"],
                "/payments/{id}/refund": ["POST"],
                "/transfers": ["POST", "GET"],
                "/transfers/{id}": ["GET"],
                "/balances": ["GET"],
                "/accounts": ["GET", "POST"],
                "/webhooks": ["POST", "GET", "PUT", "DELETE"]
            },
            APIVersion.V2: {
                "/payments": ["POST", "GET"],
                "/payments/{id}": ["GET", "PUT", "DELETE"],
                "/payments/{id}/refund": ["POST"],
                "/payments/bulk": ["POST"],
                "/transfers": ["POST", "GET"],
                "/transfers/{id}": ["GET", "PUT"],
                "/transfers/bulk": ["POST"],
                "/balances": ["GET"],
                "/balances/history": ["GET"],
                "/accounts": ["GET", "POST", "PUT"],
                "/accounts/{id}/statements": ["GET"],
                "/webhooks": ["POST", "GET", "PUT", "DELETE"],
                "/reports": ["GET", "POST"],
                "/analytics": ["GET"]
            }
        }
        
        # JWT configuration
        self.jwt_secret = "your_jwt_secret_key"
        self.jwt_algorithm = "HS256"
        self.jwt_expiry = 3600  # 1 hour
        
        # Statistics
        self.requests_processed = 0
        self.requests_failed = 0
        self.partners_active = 0
        
        # Rate limiting storage (in production, use Redis)
        self.rate_limit_storage = {}
    
    async def register_partner(self, config: PartnerConfig) -> bool:
        """تسجيل شريك جديد"""
        try:
            logger.info(f"🤝 Registering partner: {config.partner_name}")
            
            # Generate API credentials if not provided
            if not config.api_key:
                config.api_key = self._generate_api_key()
            
            if not config.secret_key:
                config.secret_key = self._generate_secret_key()
            
            # Store partner configuration
            await self._store_partner_config(config)
            
            # Create initial webhook configuration
            if config.webhook_url:
                await self._setup_webhook_config(config.partner_id, config.webhook_url)
            
            # Initialize rate limiting
            await self._initialize_rate_limiting(config.partner_id, config.rate_limit)
            
            logger.info(f"✅ Partner registered: {config.partner_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to register partner: {e}")
            return False
    
    async def authenticate_request(self, request: APIRequest) -> Tuple[bool, Optional[PartnerConfig]]:
        """مصادقة طلب API"""
        try:
            # Get partner configuration
            partner_config = await self._get_partner_config(request.partner_id)
            if not partner_config or not partner_config.is_active:
                return False, None
            
            # Check IP whitelist
            if partner_config.allowed_ips and request.ip_address not in partner_config.allowed_ips:
                logger.warning(f"⚠️ IP not allowed: {request.ip_address} for partner {request.partner_id}")
                return False, None
            
            # Authenticate based on method
            if partner_config.auth_method == AuthMethod.API_KEY:
                return await self._authenticate_api_key(request, partner_config)
            elif partner_config.auth_method == AuthMethod.JWT:
                return await self._authenticate_jwt(request, partner_config)
            elif partner_config.auth_method == AuthMethod.OAUTH2:
                return await self._authenticate_oauth2(request, partner_config)
            elif partner_config.auth_method == AuthMethod.MUTUAL_TLS:
                return await self._authenticate_mutual_tls(request, partner_config)
            
            return False, None
            
        except Exception as e:
            logger.error(f"❌ Authentication failed: {e}")
            return False, None
    
    async def check_rate_limit(self, partner_id: str, rate_limit: RateLimit) -> Tuple[bool, Dict[str, Any]]:
        """فحص حدود المعدل"""
        try:
            current_time = datetime.now()
            minute_key = f"{partner_id}:{current_time.strftime('%Y%m%d%H%M')}"
            
            # Get current minute's request count
            current_count = self.rate_limit_storage.get(minute_key, 0)
            
            # Get rate limit configuration
            limit_config = self.rate_limits[rate_limit]
            max_requests = limit_config["requests_per_minute"]
            burst_limit = limit_config["burst"]
            
            # Check if within limits
            if current_count >= max_requests:
                return False, {
                    "error": "Rate limit exceeded",
                    "limit": max_requests,
                    "current": current_count,
                    "reset_time": (current_time + timedelta(minutes=1)).isoformat()
                }
            
            # Check burst limit (requests in last 10 seconds)
            burst_count = 0
            for i in range(10):
                second_key = f"{partner_id}:{(current_time - timedelta(seconds=i)).strftime('%Y%m%d%H%M%S')}"
                burst_count += self.rate_limit_storage.get(second_key, 0)
            
            if burst_count >= burst_limit:
                return False, {
                    "error": "Burst limit exceeded",
                    "burst_limit": burst_limit,
                    "current": burst_count,
                    "reset_time": (current_time + timedelta(seconds=10)).isoformat()
                }
            
            # Increment counters
            self.rate_limit_storage[minute_key] = current_count + 1
            second_key = f"{partner_id}:{current_time.strftime('%Y%m%d%H%M%S')}"
            self.rate_limit_storage[second_key] = self.rate_limit_storage.get(second_key, 0) + 1
            
            # Clean up old entries (keep only last 2 minutes)
            cleanup_time = current_time - timedelta(minutes=2)
            keys_to_remove = [
                key for key in self.rate_limit_storage.keys()
                if key.startswith(f"{partner_id}:") and 
                datetime.strptime(key.split(':')[1][:12], '%Y%m%d%H%M') < cleanup_time
            ]
            
            for key in keys_to_remove:
                del self.rate_limit_storage[key]
            
            return True, {
                "remaining": max_requests - current_count - 1,
                "limit": max_requests,
                "reset_time": (current_time + timedelta(minutes=1)).isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Rate limit check failed: {e}")
            return False, {"error": "Rate limit check failed"}
    
    async def validate_endpoint(self, api_version: APIVersion, endpoint: str, method: str) -> bool:
        """التحقق من صحة endpoint"""
        try:
            endpoints = self.api_endpoints.get(api_version, {})
            
            # Check exact match first
            if endpoint in endpoints and method in endpoints[endpoint]:
                return True
            
            # Check parameterized endpoints
            for pattern, methods in endpoints.items():
                if self._match_endpoint_pattern(endpoint, pattern) and method in methods:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Endpoint validation failed: {e}")
            return False
    
    async def process_api_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> APIResponse:
        """معالجة طلب API"""
        try:
            start_time = datetime.now()
            request_id = f"req_{uuid.uuid4().hex[:12]}"
            
            logger.info(f"🔄 Processing API request: {request.method} {request.endpoint}")
            
            # Log request
            await self._log_api_request(request_id, request, partner_config)
            
            # Validate endpoint
            if not await self.validate_endpoint(partner_config.api_version, request.endpoint, request.method):
                return APIResponse(
                    status_code=404,
                    data={"error": "Endpoint not found", "code": "ENDPOINT_NOT_FOUND"},
                    headers={"Content-Type": "application/json"},
                    processing_time=0.0,
                    request_id=request_id
                )
            
            # Route request to appropriate handler
            response_data = await self._route_request(request, partner_config)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create response
            response = APIResponse(
                status_code=200,
                data=response_data,
                headers={
                    "Content-Type": "application/json",
                    "X-Request-ID": request_id,
                    "X-Processing-Time": str(processing_time),
                    "X-API-Version": partner_config.api_version.value
                },
                processing_time=processing_time,
                request_id=request_id
            )
            
            # Log response
            await self._log_api_response(request_id, response)
            
            # Update statistics
            self.requests_processed += 1
            
            logger.info(f"✅ API request processed: {request_id} in {processing_time:.3f}s")
            return response
            
        except Exception as e:
            logger.error(f"❌ API request processing failed: {e}")
            self.requests_failed += 1
            
            return APIResponse(
                status_code=500,
                data={"error": "Internal server error", "code": "INTERNAL_ERROR"},
                headers={"Content-Type": "application/json"},
                processing_time=0.0,
                request_id=request_id if 'request_id' in locals() else "unknown"
            )
    
    async def generate_jwt_token(self, partner_id: str, claims: Dict[str, Any] = None) -> str:
        """إنشاء JWT token"""
        try:
            payload = {
                "partner_id": partner_id,
                "iat": datetime.utcnow(),
                "exp": datetime.utcnow() + timedelta(seconds=self.jwt_expiry),
                "jti": uuid.uuid4().hex
            }
            
            if claims:
                payload.update(claims)
            
            token = jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
            
            # Store token for validation
            await self._store_jwt_token(partner_id, payload["jti"], payload["exp"])
            
            return token
            
        except Exception as e:
            logger.error(f"❌ JWT token generation failed: {e}")
            raise
    
    async def validate_jwt_token(self, token: str) -> Tuple[bool, Dict[str, Any]]:
        """التحقق من JWT token"""
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            # Check if token is revoked
            is_valid = await self._is_jwt_token_valid(payload["partner_id"], payload["jti"])
            
            if not is_valid:
                return False, {"error": "Token revoked"}
            
            return True, payload
            
        except jwt.ExpiredSignatureError:
            return False, {"error": "Token expired"}
        except jwt.InvalidTokenError:
            return False, {"error": "Invalid token"}
        except Exception as e:
            logger.error(f"❌ JWT validation failed: {e}")
            return False, {"error": "Token validation failed"}
    
    async def revoke_jwt_token(self, partner_id: str, jti: str) -> bool:
        """إلغاء JWT token"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE jwt_tokens 
                    SET is_revoked = true, revoked_at = CURRENT_TIMESTAMP
                    WHERE partner_id = $1 AND jti = $2
                """
                
                await conn.execute(query, partner_id, jti)
                return True
                
        except Exception as e:
            logger.error(f"❌ JWT token revocation failed: {e}")
            return False
    
    async def get_partner_analytics(
        self, 
        partner_id: str, 
        start_date: datetime = None, 
        end_date: datetime = None
    ) -> Dict[str, Any]:
        """الحصول على تحليلات الشريك"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build WHERE clause
                where_conditions = ["partner_id = $1"]
                params = [partner_id]
                param_count = 1
                
                if start_date:
                    param_count += 1
                    where_conditions.append(f"created_at >= ${param_count}")
                    params.append(start_date)
                
                if end_date:
                    param_count += 1
                    where_conditions.append(f"created_at <= ${param_count}")
                    params.append(end_date)
                
                where_clause = " AND ".join(where_conditions)
                
                # Request statistics
                request_stats_query = f"""
                    SELECT 
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN status_code < 400 THEN 1 END) as successful_requests,
                        COUNT(CASE WHEN status_code >= 400 THEN 1 END) as failed_requests,
                        AVG(processing_time) as avg_processing_time,
                        MAX(processing_time) as max_processing_time
                    FROM api_request_logs 
                    WHERE {where_clause}
                """
                
                request_stats = await conn.fetchrow(request_stats_query, *params)
                
                # Endpoint usage
                endpoint_stats_query = f"""
                    SELECT 
                        endpoint,
                        method,
                        COUNT(*) as request_count,
                        AVG(processing_time) as avg_time
                    FROM api_request_logs 
                    WHERE {where_clause}
                    GROUP BY endpoint, method
                    ORDER BY request_count DESC
                    LIMIT 10
                """
                
                endpoint_stats = await conn.fetch(endpoint_stats_query, *params)
                
                # Daily usage trend
                daily_stats_query = f"""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as requests,
                        COUNT(CASE WHEN status_code < 400 THEN 1 END) as successful,
                        AVG(processing_time) as avg_time
                    FROM api_request_logs 
                    WHERE {where_clause}
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                    LIMIT 30
                """
                
                daily_stats = await conn.fetch(daily_stats_query, *params)
                
                # Calculate success rate
                total_requests = request_stats['total_requests']
                success_rate = (request_stats['successful_requests'] / max(total_requests, 1)) * 100
                
                return {
                    "overview": {
                        "total_requests": total_requests,
                        "successful_requests": request_stats['successful_requests'],
                        "failed_requests": request_stats['failed_requests'],
                        "success_rate": round(success_rate, 2),
                        "avg_processing_time": round(float(request_stats['avg_processing_time'] or 0), 3),
                        "max_processing_time": round(float(request_stats['max_processing_time'] or 0), 3)
                    },
                    "top_endpoints": [
                        {
                            "endpoint": row['endpoint'],
                            "method": row['method'],
                            "request_count": row['request_count'],
                            "avg_processing_time": round(float(row['avg_time']), 3)
                        }
                        for row in endpoint_stats
                    ],
                    "daily_usage": [
                        {
                            "date": row['date'].isoformat(),
                            "total_requests": row['requests'],
                            "successful_requests": row['successful'],
                            "success_rate": round((row['successful'] / max(row['requests'], 1)) * 100, 2),
                            "avg_processing_time": round(float(row['avg_time']), 3)
                        }
                        for row in daily_stats
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get partner analytics: {e}")
            return {}
    
    async def get_all_partners(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """الحصول على جميع الشركاء"""
        try:
            async with self.db_connection.get_connection() as conn:
                where_clause = "WHERE is_active = true" if active_only else ""
                
                query = f"""
                    SELECT 
                        partner_id, partner_name, partner_type, api_version,
                        auth_method, rate_limit, is_active, created_at
                    FROM partner_configs 
                    {where_clause}
                    ORDER BY created_at DESC
                """
                
                rows = await conn.fetch(query)
                
                return [
                    {
                        "partner_id": row['partner_id'],
                        "partner_name": row['partner_name'],
                        "partner_type": row['partner_type'],
                        "api_version": row['api_version'],
                        "auth_method": row['auth_method'],
                        "rate_limit": row['rate_limit'],
                        "is_active": row['is_active'],
                        "created_at": row['created_at'].isoformat()
                    }
                    for row in rows
                ]
                
        except Exception as e:
            logger.error(f"❌ Failed to get partners: {e}")
            return []
    
    # Helper methods
    def _generate_api_key(self) -> str:
        """إنشاء API key"""
        return f"pk_{uuid.uuid4().hex}"
    
    def _generate_secret_key(self) -> str:
        """إنشاء secret key"""
        return f"sk_{uuid.uuid4().hex}"
    
    def _match_endpoint_pattern(self, endpoint: str, pattern: str) -> bool:
        """مطابقة نمط endpoint"""
        # Simple pattern matching for parameterized endpoints
        # e.g., /payments/{id} matches /payments/123
        
        endpoint_parts = endpoint.strip('/').split('/')
        pattern_parts = pattern.strip('/').split('/')
        
        if len(endpoint_parts) != len(pattern_parts):
            return False
        
        for ep, pp in zip(endpoint_parts, pattern_parts):
            if pp.startswith('{') and pp.endswith('}'):
                continue  # Parameter match
            elif ep != pp:
                return False
        
        return True
    
    async def _store_partner_config(self, config: PartnerConfig):
        """حفظ إعدادات الشريك"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO partner_configs (
                        partner_id, partner_name, partner_type, api_version,
                        auth_method, rate_limit, api_key, secret_key,
                        webhook_url, allowed_ips, is_active, metadata
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    ON CONFLICT (partner_id) DO UPDATE SET
                        partner_name = EXCLUDED.partner_name,
                        partner_type = EXCLUDED.partner_type,
                        api_version = EXCLUDED.api_version,
                        auth_method = EXCLUDED.auth_method,
                        rate_limit = EXCLUDED.rate_limit,
                        webhook_url = EXCLUDED.webhook_url,
                        allowed_ips = EXCLUDED.allowed_ips,
                        is_active = EXCLUDED.is_active,
                        metadata = EXCLUDED.metadata,
                        updated_at = CURRENT_TIMESTAMP
                """
                
                await conn.execute(
                    query,
                    config.partner_id,
                    config.partner_name,
                    config.partner_type.value,
                    config.api_version.value,
                    config.auth_method.value,
                    config.rate_limit.value,
                    config.api_key,
                    config.secret_key,
                    config.webhook_url,
                    config.allowed_ips,
                    config.is_active,
                    json.dumps(config.metadata or {})
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store partner config: {e}")
            raise
    
    async def _get_partner_config(self, partner_id: str) -> Optional[PartnerConfig]:
        """الحصول على إعدادات الشريك"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM partner_configs WHERE partner_id = $1
                """
                
                row = await conn.fetchrow(query, partner_id)
                
                if row:
                    return PartnerConfig(
                        partner_id=row['partner_id'],
                        partner_name=row['partner_name'],
                        partner_type=PartnerType(row['partner_type']),
                        api_version=APIVersion(row['api_version']),
                        auth_method=AuthMethod(row['auth_method']),
                        rate_limit=RateLimit(row['rate_limit']),
                        api_key=row['api_key'],
                        secret_key=row['secret_key'],
                        webhook_url=row['webhook_url'],
                        allowed_ips=row['allowed_ips'],
                        is_active=row['is_active'],
                        metadata=json.loads(row['metadata'] or '{}')
                    )
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to get partner config: {e}")
            return None
    
    async def _authenticate_api_key(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Tuple[bool, Optional[PartnerConfig]]:
        """مصادقة API key"""
        auth_header = request.headers.get('Authorization', '')
        
        if not auth_header.startswith('Bearer '):
            return False, None
        
        provided_key = auth_header[7:]  # Remove 'Bearer '
        
        if provided_key == partner_config.api_key:
            return True, partner_config
        
        return False, None
    
    async def _authenticate_jwt(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Tuple[bool, Optional[PartnerConfig]]:
        """مصادقة JWT"""
        auth_header = request.headers.get('Authorization', '')
        
        if not auth_header.startswith('Bearer '):
            return False, None
        
        token = auth_header[7:]  # Remove 'Bearer '
        
        is_valid, payload = await self.validate_jwt_token(token)
        
        if is_valid and payload.get('partner_id') == partner_config.partner_id:
            return True, partner_config
        
        return False, None
    
    async def _authenticate_oauth2(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Tuple[bool, Optional[PartnerConfig]]:
        """مصادقة OAuth2"""
        # Simplified OAuth2 implementation
        # In production, integrate with proper OAuth2 provider
        return await self._authenticate_jwt(request, partner_config)
    
    async def _authenticate_mutual_tls(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Tuple[bool, Optional[PartnerConfig]]:
        """مصادقة Mutual TLS"""
        # Check for client certificate information in headers
        # This would be set by the reverse proxy/load balancer
        client_cert = request.headers.get('X-Client-Cert')
        
        if client_cert:
            # Verify certificate against partner's registered certificate
            # Simplified implementation
            return True, partner_config
        
        return False, None
    
    async def _setup_webhook_config(self, partner_id: str, webhook_url: str):
        """إعداد webhook"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO partner_webhooks (
                        partner_id, webhook_url, is_active
                    ) VALUES ($1, $2, $3)
                    ON CONFLICT (partner_id) DO UPDATE SET
                        webhook_url = EXCLUDED.webhook_url,
                        is_active = EXCLUDED.is_active,
                        updated_at = CURRENT_TIMESTAMP
                """
                
                await conn.execute(query, partner_id, webhook_url, True)
                
        except Exception as e:
            logger.error(f"❌ Failed to setup webhook: {e}")
    
    async def _initialize_rate_limiting(self, partner_id: str, rate_limit: RateLimit):
        """تهيئة حدود المعدل"""
        # Initialize rate limiting counters
        self.rate_limit_storage[f"{partner_id}:init"] = 0
    
    async def _route_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Dict[str, Any]:
        """توجيه الطلب"""
        # Route to appropriate service based on endpoint
        if request.endpoint.startswith('/payments'):
            return await self._handle_payments_request(request, partner_config)
        elif request.endpoint.startswith('/transfers'):
            return await self._handle_transfers_request(request, partner_config)
        elif request.endpoint.startswith('/balances'):
            return await self._handle_balances_request(request, partner_config)
        elif request.endpoint.startswith('/accounts'):
            return await self._handle_accounts_request(request, partner_config)
        elif request.endpoint.startswith('/webhooks'):
            return await self._handle_webhooks_request(request, partner_config)
        elif request.endpoint.startswith('/reports'):
            return await self._handle_reports_request(request, partner_config)
        elif request.endpoint.startswith('/analytics'):
            return await self._handle_analytics_request(request, partner_config)
        else:
            return {"error": "Endpoint not implemented", "code": "NOT_IMPLEMENTED"}
    
    async def _handle_payments_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Dict[str, Any]:
        """معالجة طلبات الدفع"""
        # Simplified implementation - integrate with actual payment service
        if request.method == "POST":
            return {
                "payment_id": f"pay_{uuid.uuid4().hex[:12]}",
                "status": "pending",
                "amount": request.body.get("amount"),
                "currency": request.body.get("currency", "SAR"),
                "created_at": datetime.now().isoformat()
            }
        elif request.method == "GET":
            return {
                "payments": [
                    {
                        "payment_id": f"pay_{uuid.uuid4().hex[:12]}",
                        "status": "completed",
                        "amount": 1000.00,
                        "currency": "SAR",
                        "created_at": datetime.now().isoformat()
                    }
                ],
                "total": 1,
                "page": 1
            }
        
        return {"error": "Method not allowed", "code": "METHOD_NOT_ALLOWED"}
    
    async def _handle_transfers_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Dict[str, Any]:
        """معالجة طلبات التحويل"""
        # Simplified implementation
        if request.method == "POST":
            return {
                "transfer_id": f"txf_{uuid.uuid4().hex[:12]}",
                "status": "processing",
                "amount": request.body.get("amount"),
                "currency": request.body.get("currency", "SAR"),
                "created_at": datetime.now().isoformat()
            }
        
        return {"error": "Method not allowed", "code": "METHOD_NOT_ALLOWED"}
    
    async def _handle_balances_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Dict[str, Any]:
        """معالجة طلبات الأرصدة"""
        return {
            "balances": [
                {
                    "account_id": "acc_123",
                    "available_balance": 15000.00,
                    "currency": "SAR",
                    "last_updated": datetime.now().isoformat()
                }
            ]
        }
    
    async def _handle_accounts_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Dict[str, Any]:
        """معالجة طلبات الحسابات"""
        return {
            "accounts": [
                {
                    "account_id": "acc_123",
                    "account_type": "business",
                    "status": "active",
                    "created_at": datetime.now().isoformat()
                }
            ]
        }
    
    async def _handle_webhooks_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Dict[str, Any]:
        """معالجة طلبات webhook"""
        return {
            "webhooks": [
                {
                    "webhook_id": "wh_123",
                    "url": partner_config.webhook_url,
                    "events": ["payment.completed", "transfer.failed"],
                    "is_active": True
                }
            ]
        }
    
    async def _handle_reports_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Dict[str, Any]:
        """معالجة طلبات التقارير"""
        return {
            "reports": [
                {
                    "report_id": "rpt_123",
                    "type": "transactions",
                    "status": "completed",
                    "created_at": datetime.now().isoformat()
                }
            ]
        }
    
    async def _handle_analytics_request(
        self, 
        request: APIRequest, 
        partner_config: PartnerConfig
    ) -> Dict[str, Any]:
        """معالجة طلبات التحليلات"""
        return await self.get_partner_analytics(partner_config.partner_id)
    
    async def _log_api_request(self, request_id: str, request: APIRequest, partner_config: PartnerConfig):
        """تسجيل طلب API"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO api_request_logs (
                        request_id, partner_id, endpoint, method, headers,
                        body, ip_address, user_agent
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """
                
                await conn.execute(
                    query,
                    request_id,
                    request.partner_id,
                    request.endpoint,
                    request.method,
                    json.dumps(request.headers),
                    json.dumps(request.body),
                    request.ip_address,
                    request.user_agent
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to log API request: {e}")
    
    async def _log_api_response(self, request_id: str, response: APIResponse):
        """تسجيل استجابة API"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE api_request_logs 
                    SET status_code = $1, 
                        response_data = $2,
                        processing_time = $3,
                        completed_at = CURRENT_TIMESTAMP
                    WHERE request_id = $4
                """
                
                await conn.execute(
                    query,
                    response.status_code,
                    json.dumps(response.data),
                    response.processing_time,
                    request_id
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to log API response: {e}")
    
    async def _store_jwt_token(self, partner_id: str, jti: str, exp: datetime):
        """حفظ JWT token"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO jwt_tokens (
                        partner_id, jti, expires_at, is_revoked
                    ) VALUES ($1, $2, $3, $4)
                """
                
                await conn.execute(query, partner_id, jti, exp, False)
                
        except Exception as e:
            logger.error(f"❌ Failed to store JWT token: {e}")
    
    async def _is_jwt_token_valid(self, partner_id: str, jti: str) -> bool:
        """التحقق من صحة JWT token"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT is_revoked FROM jwt_tokens 
                    WHERE partner_id = $1 AND jti = $2 AND expires_at > CURRENT_TIMESTAMP
                """
                
                row = await conn.fetchrow(query, partner_id, jti)
                
                return row and not row['is_revoked']
                
        except Exception as e:
            logger.error(f"❌ Failed to validate JWT token: {e}")
            return False
    
    async def get_service_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الخدمة"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Count active partners
                partners_query = "SELECT COUNT(*) FROM partner_configs WHERE is_active = true"
                partners_count = await conn.fetchval(partners_query)

                return {
                    "requests_processed": self.requests_processed,
                    "requests_failed": self.requests_failed,
                    "success_rate": (self.requests_processed / max(self.requests_processed + self.requests_failed, 1)) * 100,
                    "active_partners": partners_count,
                    "supported_api_versions": len(self.api_endpoints),
                    "rate_limit_entries": len(self.rate_limit_storage)
                }

        except Exception as e:
            logger.error(f"❌ Failed to get service statistics: {e}")
            return {
                "requests_processed": self.requests_processed,
                "requests_failed": self.requests_failed,
                "success_rate": 0,
                "active_partners": 0,
                "supported_api_versions": len(self.api_endpoints),
                "rate_limit_entries": len(self.rate_limit_storage)
            }
