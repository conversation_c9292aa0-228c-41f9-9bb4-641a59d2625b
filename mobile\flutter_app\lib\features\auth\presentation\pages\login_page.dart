import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:reactive_forms/reactive_forms.dart';
import 'package:local_auth/local_auth.dart';

import '../bloc/auth_bloc.dart';
import '../widgets/auth_header.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/auth_button.dart';
import '../widgets/biometric_button.dart';
import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/loading_overlay.dart';
import '../../../../core/widgets/custom_snackbar.dart';
import '../../../../generated/l10n.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final LocalAuthentication _localAuth = LocalAuthentication();
  bool _biometricAvailable = false;
  bool _biometricEnabled = false;

  // نموذج النموذج
  late FormGroup _form;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeForm();
    _checkBiometricAvailability();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));

    _animationController.forward();
  }

  void _initializeForm() {
    _form = FormGroup({
      'email': FormControl<String>(
        validators: [
          Validators.required,
          Validators.email,
        ],
      ),
      'password': FormControl<String>(
        validators: [
          Validators.required,
          Validators.minLength(6),
        ],
      ),
      'rememberMe': FormControl<bool>(value: false),
    });
  }

  Future<void> _checkBiometricAvailability() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      
      setState(() {
        _biometricAvailable = isAvailable && isDeviceSupported;
      });

      // التحقق من تفعيل المصادقة البيومترية للمستخدم
      // TODO: جلب هذه المعلومة من التخزين المحلي أو API
      setState(() {
        _biometricEnabled = false; // مؤقتاً
      });
    } catch (e) {
      setState(() {
        _biometricAvailable = false;
      });
    }
  }

  void _handleLogin() {
    if (_form.valid) {
      final email = _form.control('email').value as String;
      final password = _form.control('password').value as String;
      final rememberMe = _form.control('rememberMe').value as bool;

      context.read<AuthBloc>().add(AuthLoginRequested(
        email: email,
        password: password,
        rememberMe: rememberMe,
      ));
    } else {
      _form.markAllAsTouched();
    }
  }

  void _handleBiometricLogin() {
    context.read<AuthBloc>().add(const AuthBiometricLoginRequested());
  }

  void _navigateToRegister() {
    AppRouter.goToRegister();
  }

  void _navigateToForgotPassword() {
    // TODO: تنفيذ صفحة نسيان كلمة المرور
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthError) {
            CustomSnackbar.showError(context, state.message);
          } else if (state is AuthAuthenticated) {
            AppRouter.goToDashboard();
          } else if (state is AuthOtpRequired) {
            AppRouter.router.push('/auth/verify-otp', extra: {
              'phoneNumber': state.phone,
              'type': state.verificationType,
            });
          } else if (state is AuthBiometricSetupRequired) {
            AppRouter.router.push('/auth/biometric-setup');
          }
        },
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return LoadingOverlay(
              isLoading: state is AuthLoading,
              child: SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const SizedBox(height: 40),
                      
                      // رأس الصفحة
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: const AuthHeader(
                          title: 'مرحباً بك مرة أخرى',
                          subtitle: 'سجل دخولك للوصول إلى حسابك',
                        ),
                      ),
                      
                      const SizedBox(height: 48),
                      
                      // النموذج
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: ReactiveForm(
                            formGroup: _form,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // البريد الإلكتروني
                                AuthTextField(
                                  formControlName: 'email',
                                  label: S.of(context).email,
                                  hintText: '<EMAIL>',
                                  keyboardType: TextInputType.emailAddress,
                                  prefixIcon: Icons.email_outlined,
                                ),
                                
                                const SizedBox(height: 20),
                                
                                // كلمة المرور
                                AuthTextField(
                                  formControlName: 'password',
                                  label: S.of(context).password,
                                  hintText: '••••••••',
                                  isPassword: true,
                                  prefixIcon: Icons.lock_outline,
                                ),
                                
                                const SizedBox(height: 16),
                                
                                // تذكرني ونسيان كلمة المرور
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    // تذكرني
                                    ReactiveCheckboxListTile<bool>(
                                      formControlName: 'rememberMe',
                                      title: Text(
                                        'تذكرني',
                                        style: AppTextStyles.bodyMedium,
                                      ),
                                      contentPadding: EdgeInsets.zero,
                                      controlAffinity: ListTileControlAffinity.leading,
                                      activeColor: AppColors.primary,
                                    ),
                                    
                                    // نسيان كلمة المرور
                                    TextButton(
                                      onPressed: _navigateToForgotPassword,
                                      child: Text(
                                        'نسيت كلمة المرور؟',
                                        style: AppTextStyles.linkText(context),
                                      ),
                                    ),
                                  ],
                                ),
                                
                                const SizedBox(height: 32),
                                
                                // زر تسجيل الدخول
                                AuthButton(
                                  text: 'تسجيل الدخول',
                                  onPressed: _handleLogin,
                                  isLoading: state is AuthLoading,
                                ),
                                
                                // المصادقة البيومترية
                                if (_biometricAvailable && _biometricEnabled) ...[
                                  const SizedBox(height: 24),
                                  
                                  Row(
                                    children: [
                                      const Expanded(child: Divider()),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 16),
                                        child: Text(
                                          'أو',
                                          style: AppTextStyles.bodySmall,
                                        ),
                                      ),
                                      const Expanded(child: Divider()),
                                    ],
                                  ),
                                  
                                  const SizedBox(height: 24),
                                  
                                  BiometricButton(
                                    onPressed: _handleBiometricLogin,
                                    isLoading: state is AuthLoading,
                                  ),
                                ],
                                
                                const SizedBox(height: 48),
                                
                                // رابط التسجيل
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      'ليس لديك حساب؟ ',
                                      style: AppTextStyles.bodyMedium,
                                    ),
                                    TextButton(
                                      onPressed: _navigateToRegister,
                                      child: Text(
                                        'إنشاء حساب جديد',
                                        style: AppTextStyles.linkText(context),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
