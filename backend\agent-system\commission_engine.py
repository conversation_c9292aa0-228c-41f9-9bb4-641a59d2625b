"""
Commission Engine
================
محرك العمولات المتقدم للوكلاء
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

import asyncpg
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)


class CommissionStatus(Enum):
    """حالات العمولة"""
    PENDING = "pending"
    APPROVED = "approved"
    PAID = "paid"
    CANCELLED = "cancelled"


class CommissionTier(Enum):
    """مستويات العمولة"""
    BRONZE = "bronze"
    SILVER = "silver"
    GOLD = "gold"
    PLATINUM = "platinum"
    DIAMOND = "diamond"


@dataclass
class CommissionRule:
    """قاعدة العمولة"""
    id: str
    name: str
    agent_type: str
    tier: CommissionTier
    min_volume: Decimal
    max_volume: Optional[Decimal]
    base_rate: Decimal
    tier_rate: Decimal
    volume_bonus_rate: Decimal
    is_active: bool
    effective_from: date
    effective_to: Optional[date]


@dataclass
class CommissionCalculation:
    """حساب العمولة"""
    agent_id: str
    transaction_id: str
    base_amount: Decimal
    tier_amount: Decimal
    volume_bonus: Decimal
    special_bonus: Decimal
    total_amount: Decimal
    applied_rules: List[str]
    calculation_details: Dict[str, Any]


class CommissionEngine:
    """محرك العمولات المتقدم"""
    
    def __init__(self, db_connection: DatabaseConnection):
        self.db_connection = db_connection
        
        # Commission tiers configuration
        self.tier_thresholds = {
            CommissionTier.BRONZE: {'min_volume': Decimal('0'), 'max_volume': Decimal('100000')},
            CommissionTier.SILVER: {'min_volume': Decimal('100000'), 'max_volume': Decimal('500000')},
            CommissionTier.GOLD: {'min_volume': Decimal('500000'), 'max_volume': Decimal('1000000')},
            CommissionTier.PLATINUM: {'min_volume': Decimal('1000000'), 'max_volume': Decimal('5000000')},
            CommissionTier.DIAMOND: {'min_volume': Decimal('5000000'), 'max_volume': None}
        }
        
        # Volume bonus thresholds
        self.volume_bonus_thresholds = [
            {'min_volume': Decimal('50000'), 'bonus_rate': Decimal('0.0010')},   # 0.1%
            {'min_volume': Decimal('100000'), 'bonus_rate': Decimal('0.0020')},  # 0.2%
            {'min_volume': Decimal('250000'), 'bonus_rate': Decimal('0.0035')},  # 0.35%
            {'min_volume': Decimal('500000'), 'bonus_rate': Decimal('0.0050')},  # 0.5%
            {'min_volume': Decimal('1000000'), 'bonus_rate': Decimal('0.0075')}, # 0.75%
        ]
        
        # Statistics
        self.total_commissions_calculated = 0
        self.total_commission_amount = Decimal('0')
        self.total_commissions_paid = Decimal('0')
    
    async def calculate_commission(
        self,
        agent_id: str,
        transaction_id: str,
        transaction_amount: Decimal,
        transaction_type: str = 'transfer'
    ) -> Optional[CommissionCalculation]:
        """حساب العمولة الشاملة للوكيل"""
        try:
            logger.info(f"💰 Calculating comprehensive commission for agent: {agent_id}")
            
            # Get agent profile and current tier
            agent_profile = await self._get_agent_profile(agent_id)
            if not agent_profile:
                logger.error(f"❌ Agent profile not found: {agent_id}")
                return None
            
            # Get agent's current tier based on monthly volume
            current_tier = await self._get_agent_tier(agent_id)
            
            # Get applicable commission rules
            commission_rules = await self._get_commission_rules(
                agent_profile['agent_type'], 
                current_tier, 
                transaction_amount
            )
            
            # Calculate base commission
            base_amount = await self._calculate_base_commission(
                agent_profile, 
                transaction_amount, 
                commission_rules
            )
            
            # Calculate tier commission (from downline agents)
            tier_amount = await self._calculate_tier_commission(
                agent_id, 
                transaction_amount, 
                commission_rules
            )
            
            # Calculate volume bonus
            volume_bonus = await self._calculate_volume_bonus(
                agent_id, 
                transaction_amount
            )
            
            # Calculate special bonuses
            special_bonus = await self._calculate_special_bonus(
                agent_id, 
                transaction_amount, 
                transaction_type
            )
            
            # Total commission
            total_amount = base_amount + tier_amount + volume_bonus + special_bonus
            
            # Prepare calculation details
            calculation_details = {
                'agent_tier': current_tier.value,
                'transaction_type': transaction_type,
                'base_rate': float(agent_profile['base_commission_rate']),
                'tier_rate': float(agent_profile['tier_commission_rate']),
                'volume_bonus_rate': float(agent_profile['volume_bonus_rate']),
                'applied_rules': [rule['name'] for rule in commission_rules],
                'calculation_timestamp': datetime.now().isoformat()
            }
            
            # Create commission calculation object
            commission_calc = CommissionCalculation(
                agent_id=agent_id,
                transaction_id=transaction_id,
                base_amount=base_amount,
                tier_amount=tier_amount,
                volume_bonus=volume_bonus,
                special_bonus=special_bonus,
                total_amount=total_amount,
                applied_rules=[rule['name'] for rule in commission_rules],
                calculation_details=calculation_details
            )
            
            # Store commission record
            await self._store_commission_record(commission_calc)
            
            # Update statistics
            self.total_commissions_calculated += 1
            self.total_commission_amount += total_amount
            
            logger.info(f"✅ Commission calculated: {total_amount} SAR")
            return commission_calc
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate commission: {e}")
            return None
    
    async def calculate_hierarchical_commissions(
        self,
        transaction_id: str,
        primary_agent_id: str,
        transaction_amount: Decimal
    ) -> List[CommissionCalculation]:
        """حساب العمولات الهرمية لجميع المستويات"""
        try:
            logger.info(f"🏗️ Calculating hierarchical commissions for transaction: {transaction_id}")
            
            commissions = []
            
            # Get agent hierarchy (up to 5 levels)
            hierarchy = await self._get_agent_hierarchy_upward(primary_agent_id, max_levels=5)
            
            for level, agent_data in enumerate(hierarchy):
                agent_id = agent_data['id']
                
                # Calculate commission for this level
                if level == 0:
                    # Primary agent gets full commission
                    commission = await self.calculate_commission(
                        agent_id, 
                        transaction_id, 
                        transaction_amount
                    )
                else:
                    # Upper level agents get tier commission
                    tier_rate = await self._get_tier_rate_for_level(level)
                    tier_amount = transaction_amount * tier_rate
                    
                    commission = CommissionCalculation(
                        agent_id=agent_id,
                        transaction_id=transaction_id,
                        base_amount=Decimal('0'),
                        tier_amount=tier_amount,
                        volume_bonus=Decimal('0'),
                        special_bonus=Decimal('0'),
                        total_amount=tier_amount,
                        applied_rules=[f'tier_level_{level}'],
                        calculation_details={
                            'level': level,
                            'tier_rate': float(tier_rate),
                            'commission_type': 'hierarchical_tier'
                        }
                    )
                    
                    # Store tier commission
                    await self._store_commission_record(commission)
                
                if commission:
                    commissions.append(commission)
            
            logger.info(f"✅ Calculated {len(commissions)} hierarchical commissions")
            return commissions
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate hierarchical commissions: {e}")
            return []
    
    async def process_monthly_commissions(self, month: str) -> Dict[str, Any]:
        """معالجة العمولات الشهرية"""
        try:
            logger.info(f"📅 Processing monthly commissions for: {month}")
            
            # Get all pending commissions for the month
            pending_commissions = await self._get_pending_commissions(month)
            
            processed_count = 0
            total_amount = Decimal('0')
            failed_count = 0
            
            for commission in pending_commissions:
                try:
                    # Validate commission
                    if await self._validate_commission(commission):
                        # Approve commission
                        await self._approve_commission(commission['id'])
                        processed_count += 1
                        total_amount += commission['commission_amount']
                    else:
                        # Mark as failed
                        await self._mark_commission_failed(commission['id'], "Validation failed")
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"❌ Failed to process commission {commission['id']}: {e}")
                    failed_count += 1
            
            # Generate payment batches
            payment_batches = await self._generate_payment_batches(month)
            
            result = {
                'month': month,
                'processed_count': processed_count,
                'total_amount': float(total_amount),
                'failed_count': failed_count,
                'payment_batches': len(payment_batches),
                'processing_date': datetime.now().isoformat()
            }
            
            logger.info(f"✅ Monthly commission processing completed: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to process monthly commissions: {e}")
            return {'error': str(e)}
    
    async def get_agent_commission_summary(
        self,
        agent_id: str,
        start_date: date = None,
        end_date: date = None
    ) -> Dict[str, Any]:
        """الحصول على ملخص عمولات الوكيل"""
        try:
            async with self.db_connection.get_connection() as conn:
                # Build date filter
                date_filter = ""
                params = [agent_id]
                
                if start_date:
                    date_filter += " AND created_at >= $2"
                    params.append(start_date)
                
                if end_date:
                    date_filter += f" AND created_at <= ${len(params) + 1}"
                    params.append(end_date)
                
                # Get commission summary
                query = f"""
                    SELECT 
                        COUNT(*) as total_commissions,
                        SUM(commission_amount) as total_amount,
                        SUM(CASE WHEN status = 'pending' THEN commission_amount ELSE 0 END) as pending_amount,
                        SUM(CASE WHEN status = 'approved' THEN commission_amount ELSE 0 END) as approved_amount,
                        SUM(CASE WHEN status = 'paid' THEN commission_amount ELSE 0 END) as paid_amount,
                        AVG(commission_amount) as average_commission,
                        COUNT(CASE WHEN commission_type = 'direct' THEN 1 END) as direct_commissions,
                        COUNT(CASE WHEN commission_type = 'tier' THEN 1 END) as tier_commissions,
                        COUNT(CASE WHEN commission_type = 'volume_bonus' THEN 1 END) as bonus_commissions
                    FROM agent_commissions 
                    WHERE agent_id = $1 {date_filter}
                """
                
                summary = await conn.fetchrow(query, *params)
                
                # Get monthly breakdown
                monthly_query = f"""
                    SELECT 
                        commission_period,
                        COUNT(*) as commission_count,
                        SUM(commission_amount) as total_amount
                    FROM agent_commissions 
                    WHERE agent_id = $1 {date_filter}
                    GROUP BY commission_period 
                    ORDER BY commission_period DESC
                    LIMIT 12
                """
                
                monthly_data = await conn.fetch(monthly_query, *params)
                
                return {
                    'agent_id': agent_id,
                    'summary': {
                        'total_commissions': summary['total_commissions'] or 0,
                        'total_amount': float(summary['total_amount'] or 0),
                        'pending_amount': float(summary['pending_amount'] or 0),
                        'approved_amount': float(summary['approved_amount'] or 0),
                        'paid_amount': float(summary['paid_amount'] or 0),
                        'average_commission': float(summary['average_commission'] or 0),
                        'direct_commissions': summary['direct_commissions'] or 0,
                        'tier_commissions': summary['tier_commissions'] or 0,
                        'bonus_commissions': summary['bonus_commissions'] or 0
                    },
                    'monthly_breakdown': [
                        {
                            'period': row['commission_period'],
                            'count': row['commission_count'],
                            'amount': float(row['total_amount'])
                        }
                        for row in monthly_data
                    ]
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to get commission summary: {e}")
            return {'error': str(e)}
    
    # Helper methods
    async def _get_agent_profile(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على ملف الوكيل"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM agent_profiles 
                    WHERE id = $1 AND status = 'active' AND deleted_at IS NULL
                """
                
                row = await conn.fetchrow(query, agent_id)
                return dict(row) if row else None
                
        except Exception as e:
            logger.error(f"❌ Failed to get agent profile: {e}")
            return None
    
    async def _get_agent_tier(self, agent_id: str) -> CommissionTier:
        """تحديد مستوى الوكيل بناءً على الحجم الشهري"""
        try:
            # Get current month volume
            current_month = datetime.now().strftime('%Y-%m')
            
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT COALESCE(SUM(transaction_amount), 0) as monthly_volume
                    FROM agent_commissions 
                    WHERE agent_id = $1 AND commission_period = $2
                """
                
                monthly_volume = await conn.fetchval(query, agent_id, current_month) or Decimal('0')
                
                # Determine tier based on volume
                for tier, thresholds in self.tier_thresholds.items():
                    min_vol = thresholds['min_volume']
                    max_vol = thresholds['max_volume']
                    
                    if monthly_volume >= min_vol and (max_vol is None or monthly_volume < max_vol):
                        return tier
                
                return CommissionTier.BRONZE
                
        except Exception as e:
            logger.error(f"❌ Failed to get agent tier: {e}")
            return CommissionTier.BRONZE
    
    async def _get_commission_rules(
        self, 
        agent_type: str, 
        tier: CommissionTier, 
        transaction_amount: Decimal
    ) -> List[Dict[str, Any]]:
        """الحصول على قواعد العمولة المطبقة"""
        # For now, return default rules
        # In a real implementation, this would query a commission_rules table
        return [
            {
                'name': f'{agent_type}_{tier.value}_base',
                'type': 'base_commission',
                'rate': Decimal('0.0100')  # 1%
            }
        ]
    
    async def _calculate_base_commission(
        self, 
        agent_profile: Dict[str, Any], 
        transaction_amount: Decimal, 
        rules: List[Dict[str, Any]]
    ) -> Decimal:
        """حساب العمولة الأساسية"""
        base_rate = agent_profile['base_commission_rate']
        return transaction_amount * base_rate
    
    async def _calculate_tier_commission(
        self, 
        agent_id: str, 
        transaction_amount: Decimal, 
        rules: List[Dict[str, Any]]
    ) -> Decimal:
        """حساب عمولة المستوى"""
        # This would calculate commission from downline agents
        # For now, return 0
        return Decimal('0')
    
    async def _calculate_volume_bonus(self, agent_id: str, transaction_amount: Decimal) -> Decimal:
        """حساب مكافأة الحجم"""
        try:
            # Get current month volume
            current_month = datetime.now().strftime('%Y-%m')
            
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT COALESCE(SUM(transaction_amount), 0) as monthly_volume
                    FROM agent_commissions 
                    WHERE agent_id = $1 AND commission_period = $2
                """
                
                monthly_volume = await conn.fetchval(query, agent_id, current_month) or Decimal('0')
                
                # Add current transaction to volume
                total_volume = monthly_volume + transaction_amount
                
                # Find applicable bonus rate
                bonus_rate = Decimal('0')
                for threshold in reversed(self.volume_bonus_thresholds):
                    if total_volume >= threshold['min_volume']:
                        bonus_rate = threshold['bonus_rate']
                        break
                
                return transaction_amount * bonus_rate
                
        except Exception as e:
            logger.error(f"❌ Failed to calculate volume bonus: {e}")
            return Decimal('0')
    
    async def _calculate_special_bonus(
        self, 
        agent_id: str, 
        transaction_amount: Decimal, 
        transaction_type: str
    ) -> Decimal:
        """حساب المكافآت الخاصة"""
        # This could include seasonal bonuses, performance bonuses, etc.
        return Decimal('0')
    
    async def _store_commission_record(self, commission_calc: CommissionCalculation):
        """حفظ سجل العمولة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    INSERT INTO agent_commissions (
                        agent_id, transaction_id, commission_type, commission_rate,
                        transaction_amount, commission_amount, commission_period, 
                        due_date, metadata
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """
                
                commission_period = datetime.now().strftime('%Y-%m')
                due_date = (datetime.now().replace(day=1) + timedelta(days=32)).replace(day=1).date()
                
                await conn.execute(
                    query,
                    commission_calc.agent_id,
                    commission_calc.transaction_id,
                    'comprehensive',
                    commission_calc.total_amount / Decimal('100'),  # Assuming transaction amount is 100
                    commission_calc.total_amount,
                    commission_calc.total_amount,
                    commission_period,
                    due_date,
                    commission_calc.calculation_details
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store commission record: {e}")
    
    async def _get_agent_hierarchy_upward(self, agent_id: str, max_levels: int = 5) -> List[Dict[str, Any]]:
        """الحصول على الهيكل الهرمي للأعلى"""
        try:
            hierarchy = []
            current_agent_id = agent_id
            level = 0
            
            async with self.db_connection.get_connection() as conn:
                while current_agent_id and level < max_levels:
                    query = """
                        SELECT id, parent_agent_id, agent_code, level
                        FROM agent_profiles 
                        WHERE id = $1 AND status = 'active' AND deleted_at IS NULL
                    """
                    
                    agent_data = await conn.fetchrow(query, current_agent_id)
                    if not agent_data:
                        break
                    
                    hierarchy.append(dict(agent_data))
                    current_agent_id = agent_data['parent_agent_id']
                    level += 1
            
            return hierarchy
            
        except Exception as e:
            logger.error(f"❌ Failed to get agent hierarchy: {e}")
            return []
    
    async def _get_tier_rate_for_level(self, level: int) -> Decimal:
        """الحصول على معدل العمولة للمستوى"""
        tier_rates = {
            1: Decimal('0.0050'),  # 0.5%
            2: Decimal('0.0025'),  # 0.25%
            3: Decimal('0.0015'),  # 0.15%
            4: Decimal('0.0010'),  # 0.1%
            5: Decimal('0.0005'),  # 0.05%
        }
        
        return tier_rates.get(level, Decimal('0'))
    
    async def _get_pending_commissions(self, month: str) -> List[Dict[str, Any]]:
        """الحصول على العمولات المعلقة للشهر"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    SELECT * FROM agent_commissions 
                    WHERE commission_period = $1 AND status = 'pending'
                    ORDER BY created_at
                """
                
                rows = await conn.fetch(query, month)
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Failed to get pending commissions: {e}")
            return []
    
    async def _validate_commission(self, commission: Dict[str, Any]) -> bool:
        """التحقق من صحة العمولة"""
        # Add validation logic here
        return True
    
    async def _approve_commission(self, commission_id: str):
        """الموافقة على العمولة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE agent_commissions 
                    SET status = 'approved', updated_at = CURRENT_TIMESTAMP
                    WHERE id = $1
                """
                
                await conn.execute(query, commission_id)
                
        except Exception as e:
            logger.error(f"❌ Failed to approve commission: {e}")
    
    async def _mark_commission_failed(self, commission_id: str, reason: str):
        """تمييز العمولة كفاشلة"""
        try:
            async with self.db_connection.get_connection() as conn:
                query = """
                    UPDATE agent_commissions 
                    SET status = 'cancelled', 
                        metadata = metadata || $2,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = $1
                """
                
                await conn.execute(query, commission_id, {'failure_reason': reason})
                
        except Exception as e:
            logger.error(f"❌ Failed to mark commission as failed: {e}")
    
    async def _generate_payment_batches(self, month: str) -> List[str]:
        """إنشاء دفعات الدفع"""
        # This would generate payment batches for approved commissions
        return []
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات العمولات"""
        return {
            "total_commissions_calculated": self.total_commissions_calculated,
            "total_commission_amount": float(self.total_commission_amount),
            "total_commissions_paid": float(self.total_commissions_paid),
            "average_commission": float(self.total_commission_amount / max(self.total_commissions_calculated, 1))
        }
