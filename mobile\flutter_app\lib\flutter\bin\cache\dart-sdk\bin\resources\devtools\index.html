<!DOCTYPE html>

<!--
Copyright 2025 The Flutter Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file or at https://developers.google.com/open-source/licenses/bsd.
-->

<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- Note: This tag is replaced when served through DDS! -->
  <base href="/">

  <title></title>
  <link href="favicon.png" rel="icon" sizes="64x64">

  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script>
    // The value of DEVTOOLS_GOOGLE_TAG_ID here must match the value of the '?id='
    // query parameter below in the 'https://www.googletagmanager.com' script.
    // This is the value of the "Dart DevTools - GA4" Google Tag, which is linked
    // to the DevTools GA4 analytics property.
    const DEVTOOLS_GOOGLE_TAG_ID = 'G-69MPZE94D5'; // 

    function getDevToolsPropertyID() {
      return DEVTOOLS_GOOGLE_TAG_ID;
    }
  </script>
  <!-- The below URI ?id= must match the DEVTOOLS_GOOGLE_TAG_ID above. -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-69MPZE94D5"></script>
  <script src="devtools_analytics.js"></script>
  <!-- End of DevTools Google Analytics -->

  <!-- DO NOT REMOVE: -->
  <!-- OBSERVER SCRIPT PLACEHOLDER -->

  <script>
    function supportsES6Classes() {
      "use strict";
      try { eval("class Foo {}"); }
      catch (e) { return false; }
      return true;
    }

    if (!supportsES6Classes()) {
      window.location.href = '/unsupported-browser.html';
    }
  </script>

  <link rel="stylesheet" href="styles.css">

</head>

<body>
  <script src="flutter_bootstrap.js" async></script>
</body>

</html>
