The Dart SDK is a set of tools and libraries for the Dart programming language.

You can find information about <PERSON><PERSON> online at https://dart.dev/.

Here's a brief guide to what's in here:

bin/               Binaries/scripts to compile, run, and manage Dart apps.
  dart             Command line Dart tool
  dartaotruntime   Minimal Dart runtime for running AOT modules
  resources/       Resource files for dartdoc and devtools
  snapshots/       AppAOT and AppJIT snapshots of various tools
  utils/           Tools used by Dart compilers

include/           header files that define the Dart embedding API for use by
                   - C/C++ applications that embed the Dart Virtual machine
                   - native libraries loaded into a dart application using FFI
                     (https://dart.dev/guides/libraries/c-interop)

lib/               Libraries that are shipped with the Dart runtime. More
                   information is available at https://api.dart.dev.

LICENSE            Description of Dart SDK license

README             This file

revision           The git commit ID of the SDK build
                   (for example, 020b3efd3f0023c5db2097787f7cf778db837a8f).

version            The version number of the SDK (for example, 2.12.1).
