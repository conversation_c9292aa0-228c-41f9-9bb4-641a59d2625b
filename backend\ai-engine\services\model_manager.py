"""
Model Manager Service
====================
مدير النماذج المتقدم لإدارة نماذج الذكاء الاصطناعي
"""

import os
import asyncio
import json
import pickle
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import hashlib

import joblib
import tensorflow as tf
import torch
import numpy as np
from sklearn.base import BaseEstimator

from core.config import settings

logger = logging.getLogger(__name__)


class ModelInfo:
    """معلومات النموذج"""
    def __init__(
        self,
        name: str,
        version: str,
        model_type: str,
        file_path: str,
        created_at: datetime,
        size_bytes: int,
        checksum: str,
        metadata: Dict[str, Any] = None
    ):
        self.name = name
        self.version = version
        self.model_type = model_type
        self.file_path = file_path
        self.created_at = created_at
        self.size_bytes = size_bytes
        self.checksum = checksum
        self.metadata = metadata or {}
        self.last_used = datetime.now()
        self.usage_count = 0


class ModelManager:
    """مدير النماذج المتقدم"""
    
    def __init__(self):
        # Model storage
        self.loaded_models: Dict[str, Any] = {}
        self.model_info: Dict[str, ModelInfo] = {}
        
        # Configuration
        self.model_path = Path(settings.MODEL_PATH)
        self.cache_size = settings.MODEL_CACHE_SIZE
        self.reload_interval = settings.MODEL_RELOAD_INTERVAL
        
        # Statistics
        self.total_loads = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Background tasks
        self._cleanup_task = None
        self._reload_task = None
        
    async def initialize(self):
        """تهيئة مدير النماذج"""
        try:
            logger.info("🤖 Initializing Model Manager...")
            
            # Ensure model directory exists
            self.model_path.mkdir(parents=True, exist_ok=True)
            
            # Load model registry
            await self._load_model_registry()
            
            # Start background tasks
            await self._start_background_tasks()
            
            logger.info("✅ Model Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Model Manager: {e}")
            raise
    
    async def load_model(self, model_name: str, file_path: str = None) -> Any:
        """تحميل نموذج"""
        try:
            # Check if model is already loaded
            if model_name in self.loaded_models:
                self.cache_hits += 1
                model_info = self.model_info.get(model_name)
                if model_info:
                    model_info.last_used = datetime.now()
                    model_info.usage_count += 1
                
                logger.debug(f"📋 Model loaded from cache: {model_name}")
                return self.loaded_models[model_name]
            
            self.cache_misses += 1
            
            # Determine file path
            if not file_path:
                file_path = self._get_default_model_path(model_name)
            
            # Check if file exists
            if not os.path.exists(file_path):
                logger.warning(f"⚠️ Model file not found: {file_path}")
                return None
            
            # Load model based on file extension
            model = await self._load_model_from_file(file_path)
            
            if model is None:
                logger.error(f"❌ Failed to load model: {model_name}")
                return None
            
            # Store in cache
            await self._cache_model(model_name, model, file_path)
            
            self.total_loads += 1
            
            logger.info(f"✅ Model loaded successfully: {model_name}")
            return model
            
        except Exception as e:
            logger.error(f"❌ Model loading failed: {e}")
            return None
    
    async def _load_model_from_file(self, file_path: str) -> Any:
        """تحميل النموذج من الملف"""
        try:
            file_extension = Path(file_path).suffix.lower()
            
            if file_extension == '.joblib':
                # Scikit-learn models
                return joblib.load(file_path)
            
            elif file_extension == '.pkl' or file_extension == '.pickle':
                # Pickle files
                with open(file_path, 'rb') as f:
                    return pickle.load(f)
            
            elif file_extension == '.h5' or file_extension == '.hdf5':
                # Keras/TensorFlow models
                return tf.keras.models.load_model(file_path)
            
            elif file_extension == '.pb':
                # TensorFlow SavedModel
                return tf.saved_model.load(file_path)
            
            elif file_extension == '.pt' or file_extension == '.pth':
                # PyTorch models
                return torch.load(file_path, map_location='cpu')
            
            elif file_extension == '.json':
                # JSON model configurations
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            else:
                logger.error(f"❌ Unsupported model format: {file_extension}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to load model from file {file_path}: {e}")
            return None
    
    async def _cache_model(self, model_name: str, model: Any, file_path: str):
        """حفظ النموذج في الكاش"""
        try:
            # Check cache size limit
            if len(self.loaded_models) >= self.cache_size:
                await self._evict_least_used_model()
            
            # Store model
            self.loaded_models[model_name] = model
            
            # Create model info
            file_stats = os.stat(file_path)
            checksum = self._get_file_hash(file_path)
            
            model_info = ModelInfo(
                name=model_name,
                version=self._extract_version_from_path(file_path),
                model_type=self._detect_model_type(model),
                file_path=file_path,
                created_at=datetime.fromtimestamp(file_stats.st_ctime),
                size_bytes=file_stats.st_size,
                checksum=checksum
            )
            
            self.model_info[model_name] = model_info
            
            # Update registry
            await self._update_model_registry(model_name, model_info)
            
        except Exception as e:
            logger.error(f"❌ Failed to cache model: {e}")
    
    def _get_file_hash(self, file_path: str) -> str:
        """حساب hash للملف"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return "unknown"
    
    async def _evict_least_used_model(self):
        """إزالة النموذج الأقل استخداماً"""
        try:
            if not self.model_info:
                return
            
            # Find least recently used model
            lru_model = min(
                self.model_info.items(),
                key=lambda x: x[1].last_used
            )
            
            model_name = lru_model[0]
            
            # Remove from cache
            if model_name in self.loaded_models:
                del self.loaded_models[model_name]
            
            if model_name in self.model_info:
                del self.model_info[model_name]
            
            logger.info(f"🗑️ Evicted model from cache: {model_name}")
            
        except Exception as e:
            logger.error(f"❌ Model eviction failed: {e}")
    
    def _get_default_model_path(self, model_name: str) -> str:
        """الحصول على مسار النموذج الافتراضي"""
        # Try different extensions
        extensions = ['.joblib', '.h5', '.pkl', '.pt', '.pb']
        
        for ext in extensions:
            file_path = self.model_path / f"{model_name}{ext}"
            if file_path.exists():
                return str(file_path)
        
        # Return default path with .joblib extension
        return str(self.model_path / f"{model_name}.joblib")
    
    def _extract_version_from_path(self, file_path: str) -> str:
        """استخراج الإصدار من مسار الملف"""
        try:
            # Look for version pattern like v1.0.0 or _v1_2_0
            import re
            version_pattern = r'v?(\d+)[\._](\d+)[\._](\d+)'
            match = re.search(version_pattern, file_path)
            
            if match:
                return f"v{match.group(1)}.{match.group(2)}.{match.group(3)}"
            
            return "v1.0.0"  # Default version
            
        except Exception:
            return "v1.0.0"
    
    def _detect_model_type(self, model: Any) -> str:
        """كشف نوع النموذج"""
        try:
            if hasattr(model, '__class__'):
                class_name = model.__class__.__name__
                if 'keras' in class_name.lower() or 'tensorflow' in str(type(model)).lower():
                    return "keras"
                elif hasattr(model, 'predict') and hasattr(model, 'fit'):
                    return "sklearn"
                elif 'torch' in str(type(model)).lower():
                    return "pytorch"
            
            if isinstance(model, dict):
                return "custom"
            else:
                return "unknown"
                
        except Exception:
            return "unknown"
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """قائمة النماذج المحملة"""
        try:
            models_list = []
            
            for model_name, model_info in self.model_info.items():
                models_list.append({
                    "name": model_info.name,
                    "version": model_info.version,
                    "type": model_info.model_type,
                    "size_mb": round(model_info.size_bytes / (1024 * 1024), 2),
                    "created_at": model_info.created_at.isoformat(),
                    "last_used": model_info.last_used.isoformat(),
                    "usage_count": model_info.usage_count,
                    "is_loaded": model_name in self.loaded_models,
                    "file_path": model_info.file_path,
                    "checksum": model_info.checksum[:8]  # First 8 characters
                })
            
            return models_list
            
        except Exception as e:
            logger.error(f"❌ Failed to list models: {e}")
            return []
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات مدير النماذج"""
        return {
            "total_models": len(self.model_info),
            "loaded_models": len(self.loaded_models),
            "total_loads": self.total_loads,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_hit_rate": self.cache_hits / max(self.cache_hits + self.cache_misses, 1),
            "cache_size_limit": self.cache_size,
            "model_path": str(self.model_path)
        }
    
    async def cleanup(self):
        """تنظيف الموارد"""
        try:
            logger.info("🧹 Cleaning up Model Manager...")
            
            # Cancel background tasks
            if self._cleanup_task:
                self._cleanup_task.cancel()
            if self._reload_task:
                self._reload_task.cancel()
            
            # Clear caches
            self.loaded_models.clear()
            self.model_info.clear()
            
            logger.info("✅ Model Manager cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Model Manager cleanup failed: {e}")
    
    # Helper methods (simplified implementations)
    async def _load_model_registry(self):
        """تحميل سجل النماذج"""
        try:
            # This would load model registry from database or file
            pass
        except Exception as e:
            logger.error(f"❌ Failed to load model registry: {e}")
    
    async def _update_model_registry(self, model_name: str, model_info: ModelInfo):
        """تحديث سجل النماذج"""
        try:
            # This would update model registry in database or file
            pass
        except Exception as e:
            logger.error(f"❌ Failed to update model registry: {e}")
    
    async def _start_background_tasks(self):
        """بدء المهام الخلفية"""
        try:
            # Start cleanup task
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            
            # Start reload task
            self._reload_task = asyncio.create_task(self._periodic_reload())
            
        except Exception as e:
            logger.error(f"❌ Failed to start background tasks: {e}")
    
    async def _periodic_cleanup(self):
        """تنظيف دوري للكاش"""
        try:
            while True:
                await asyncio.sleep(3600)  # Every hour
                
                # Remove unused models older than 2 hours
                cutoff_time = datetime.now() - timedelta(hours=2)
                
                models_to_remove = []
                for model_name, model_info in self.model_info.items():
                    if model_info.last_used < cutoff_time and model_info.usage_count == 0:
                        models_to_remove.append(model_name)
                
                for model_name in models_to_remove:
                    await self.unload_model(model_name)
                
                if models_to_remove:
                    logger.info(f"🧹 Cleaned up {len(models_to_remove)} unused models")
                    
        except asyncio.CancelledError:
            logger.info("🛑 Periodic cleanup task cancelled")
        except Exception as e:
            logger.error(f"❌ Periodic cleanup failed: {e}")
    
    async def _periodic_reload(self):
        """إعادة تحميل دورية للنماذج"""
        try:
            while True:
                await asyncio.sleep(self.reload_interval)
                
                # Check for model file changes and reload if necessary
                for model_name, model_info in list(self.model_info.items()):
                    if os.path.exists(model_info.file_path):
                        current_checksum = self._get_file_hash(model_info.file_path)
                        if current_checksum != model_info.checksum:
                            logger.info(f"🔄 Model file changed, reloading: {model_name}")
                            await self.reload_model(model_name)
                    
        except asyncio.CancelledError:
            logger.info("🛑 Periodic reload task cancelled")
        except Exception as e:
            logger.error(f"❌ Periodic reload failed: {e}")
    
    async def unload_model(self, model_name: str) -> bool:
        """إلغاء تحميل النموذج"""
        try:
            if model_name in self.loaded_models:
                del self.loaded_models[model_name]
            
            if model_name in self.model_info:
                del self.model_info[model_name]
            
            logger.info(f"🗑️ Model unloaded: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Model unloading failed: {e}")
            return False
    
    async def reload_model(self, model_name: str) -> bool:
        """إعادة تحميل النموذج"""
        try:
            # Get current model info
            model_info = self.model_info.get(model_name)
            if not model_info:
                logger.warning(f"⚠️ Model info not found for reload: {model_name}")
                return False
            
            # Unload current model
            await self.unload_model(model_name)
            
            # Reload model
            reloaded_model = await self.load_model(model_name, model_info.file_path)
            
            if reloaded_model is not None:
                logger.info(f"🔄 Model reloaded successfully: {model_name}")
                return True
            else:
                logger.error(f"❌ Model reload failed: {model_name}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Model reload failed: {e}")
            return False
