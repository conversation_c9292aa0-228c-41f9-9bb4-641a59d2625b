"""
Logging Configuration for AI Engine
===================================
إعدادات التسجيل لمحرك الذكاء الاصطناعي
"""

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any
import json

import structlog
from pythonjsonlogger import jsonlogger

from core.config import settings


def setup_logging():
    """إعداد نظام التسجيل"""
    
    # Create logs directory
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)
    
    # Configure standard logging
    _configure_standard_logging()
    
    # Configure structured logging
    _configure_structured_logging()
    
    # Set log levels
    _set_log_levels()
    
    print("✅ Logging configured successfully")


def _configure_standard_logging():
    """إعداد التسجيل القياسي"""
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s - '
            '[%(filename)s:%(lineno)d]',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        fmt='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    json_formatter = jsonlogger.JsonFormatter(
        fmt='%(asctime)s %(name)s %(levelname)s %(message)s %(filename)s %(lineno)d',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    if settings.LOG_FORMAT == "json":
        console_handler.setFormatter(json_formatter)
    else:
        console_handler.setFormatter(simple_formatter)
    
    # File handler for all logs
    file_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join("logs", "ai_engine.log"),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    
    # Error file handler
    error_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join("logs", "ai_engine_errors.log"),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    
    # Performance file handler
    performance_handler = logging.handlers.RotatingFileHandler(
        filename=os.path.join("logs", "ai_engine_performance.log"),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=3,
        encoding='utf-8'
    )
    performance_handler.setLevel(logging.INFO)
    performance_handler.setFormatter(json_formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
    
    # Configure performance logger
    performance_logger = logging.getLogger("performance")
    performance_logger.addHandler(performance_handler)
    performance_logger.propagate = False


def _configure_structured_logging():
    """إعداد التسجيل المنظم"""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            _add_service_info,
            _add_request_id,
            structlog.processors.JSONRenderer() if settings.LOG_FORMAT == "json" 
            else structlog.dev.ConsoleRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def _set_log_levels():
    """تعيين مستويات التسجيل"""
    
    # Set log level based on configuration
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    
    # Configure specific loggers
    loggers_config = {
        "uvicorn": logging.WARNING,
        "uvicorn.access": logging.WARNING,
        "fastapi": logging.INFO,
        "tensorflow": logging.WARNING,
        "sklearn": logging.WARNING,
        "matplotlib": logging.WARNING,
        "PIL": logging.WARNING,
        "asyncpg": logging.WARNING,
        "motor": logging.WARNING,
        "elasticsearch": logging.WARNING,
        "redis": logging.WARNING,
    }
    
    for logger_name, level in loggers_config.items():
        logging.getLogger(logger_name).setLevel(level)
    
    # Set main application logger level
    logging.getLogger("ai_engine").setLevel(log_level)


def _add_service_info(logger, method_name, event_dict):
    """إضافة معلومات الخدمة"""
    event_dict["service"] = "ai-engine"
    event_dict["version"] = settings.VERSION
    event_dict["environment"] = settings.ENVIRONMENT
    return event_dict


def _add_request_id(logger, method_name, event_dict):
    """إضافة معرف الطلب"""
    # This would typically get the request ID from context
    # For now, we'll use a placeholder
    event_dict["request_id"] = "req_" + str(hash(str(datetime.now())))[-8:]
    return event_dict


class PerformanceLogger:
    """مسجل الأداء"""
    
    def __init__(self):
        self.logger = logging.getLogger("performance")
    
    def log_prediction_performance(
        self,
        model_type: str,
        processing_time: float,
        input_size: int,
        output_size: int,
        success: bool = True,
        error: str = None
    ):
        """تسجيل أداء التنبؤ"""
        self.logger.info(
            "Prediction performance",
            extra={
                "event_type": "prediction_performance",
                "model_type": model_type,
                "processing_time_ms": processing_time * 1000,
                "input_size": input_size,
                "output_size": output_size,
                "success": success,
                "error": error,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_model_load_performance(
        self,
        model_name: str,
        load_time: float,
        model_size_mb: float,
        success: bool = True,
        error: str = None
    ):
        """تسجيل أداء تحميل النموذج"""
        self.logger.info(
            "Model load performance",
            extra={
                "event_type": "model_load_performance",
                "model_name": model_name,
                "load_time_ms": load_time * 1000,
                "model_size_mb": model_size_mb,
                "success": success,
                "error": error,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_database_performance(
        self,
        operation: str,
        query_time: float,
        rows_affected: int = 0,
        success: bool = True,
        error: str = None
    ):
        """تسجيل أداء قاعدة البيانات"""
        self.logger.info(
            "Database performance",
            extra={
                "event_type": "database_performance",
                "operation": operation,
                "query_time_ms": query_time * 1000,
                "rows_affected": rows_affected,
                "success": success,
                "error": error,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_cache_performance(
        self,
        operation: str,
        cache_hit: bool,
        response_time: float,
        key: str = None
    ):
        """تسجيل أداء الكاش"""
        self.logger.info(
            "Cache performance",
            extra={
                "event_type": "cache_performance",
                "operation": operation,
                "cache_hit": cache_hit,
                "response_time_ms": response_time * 1000,
                "key": key,
                "timestamp": datetime.now().isoformat()
            }
        )


class SecurityLogger:
    """مسجل الأمان"""
    
    def __init__(self):
        self.logger = logging.getLogger("security")
        
        # Security log handler
        security_handler = logging.handlers.RotatingFileHandler(
            filename=os.path.join("logs", "ai_engine_security.log"),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        security_handler.setLevel(logging.INFO)
        security_handler.setFormatter(
            jsonlogger.JsonFormatter(
                fmt='%(asctime)s %(name)s %(levelname)s %(message)s'
            )
        )
        
        self.logger.addHandler(security_handler)
        self.logger.propagate = False
    
    def log_authentication_attempt(
        self,
        user_id: str,
        success: bool,
        ip_address: str = None,
        user_agent: str = None,
        error: str = None
    ):
        """تسجيل محاولة المصادقة"""
        self.logger.info(
            "Authentication attempt",
            extra={
                "event_type": "authentication_attempt",
                "user_id": user_id,
                "success": success,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "error": error,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_fraud_detection(
        self,
        transaction_id: str,
        user_id: str,
        fraud_score: float,
        risk_level: str,
        blocked: bool = False
    ):
        """تسجيل كشف الاحتيال"""
        self.logger.warning(
            "Fraud detection",
            extra={
                "event_type": "fraud_detection",
                "transaction_id": transaction_id,
                "user_id": user_id,
                "fraud_score": fraud_score,
                "risk_level": risk_level,
                "blocked": blocked,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_suspicious_activity(
        self,
        user_id: str,
        activity_type: str,
        description: str,
        severity: str = "medium",
        metadata: Dict[str, Any] = None
    ):
        """تسجيل النشاط المشبوه"""
        self.logger.warning(
            "Suspicious activity",
            extra={
                "event_type": "suspicious_activity",
                "user_id": user_id,
                "activity_type": activity_type,
                "description": description,
                "severity": severity,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_rate_limit_exceeded(
        self,
        user_id: str,
        endpoint: str,
        ip_address: str = None,
        requests_count: int = 0
    ):
        """تسجيل تجاوز حد المعدل"""
        self.logger.warning(
            "Rate limit exceeded",
            extra={
                "event_type": "rate_limit_exceeded",
                "user_id": user_id,
                "endpoint": endpoint,
                "ip_address": ip_address,
                "requests_count": requests_count,
                "timestamp": datetime.now().isoformat()
            }
        )


class AuditLogger:
    """مسجل التدقيق"""
    
    def __init__(self):
        self.logger = logging.getLogger("audit")
        
        # Audit log handler
        audit_handler = logging.handlers.RotatingFileHandler(
            filename=os.path.join("logs", "ai_engine_audit.log"),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=20,  # Keep more audit logs
            encoding='utf-8'
        )
        audit_handler.setLevel(logging.INFO)
        audit_handler.setFormatter(
            jsonlogger.JsonFormatter(
                fmt='%(asctime)s %(name)s %(levelname)s %(message)s'
            )
        )
        
        self.logger.addHandler(audit_handler)
        self.logger.propagate = False
    
    def log_model_prediction(
        self,
        model_type: str,
        user_id: str,
        input_data: Dict[str, Any],
        prediction_result: Dict[str, Any],
        processing_time: float
    ):
        """تسجيل تنبؤ النموذج"""
        self.logger.info(
            "Model prediction",
            extra={
                "event_type": "model_prediction",
                "model_type": model_type,
                "user_id": user_id,
                "input_data": input_data,
                "prediction_result": prediction_result,
                "processing_time_ms": processing_time * 1000,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_data_access(
        self,
        user_id: str,
        data_type: str,
        operation: str,
        records_count: int = 0,
        success: bool = True
    ):
        """تسجيل الوصول للبيانات"""
        self.logger.info(
            "Data access",
            extra={
                "event_type": "data_access",
                "user_id": user_id,
                "data_type": data_type,
                "operation": operation,
                "records_count": records_count,
                "success": success,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_configuration_change(
        self,
        user_id: str,
        component: str,
        old_value: Any,
        new_value: Any,
        change_reason: str = None
    ):
        """تسجيل تغيير الإعدادات"""
        self.logger.info(
            "Configuration change",
            extra={
                "event_type": "configuration_change",
                "user_id": user_id,
                "component": component,
                "old_value": str(old_value),
                "new_value": str(new_value),
                "change_reason": change_reason,
                "timestamp": datetime.now().isoformat()
            }
        )


# Global logger instances
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()
audit_logger = AuditLogger()


def get_logger(name: str) -> logging.Logger:
    """الحصول على مسجل"""
    return logging.getLogger(name)


def get_structured_logger(name: str):
    """الحصول على مسجل منظم"""
    return structlog.get_logger(name)
