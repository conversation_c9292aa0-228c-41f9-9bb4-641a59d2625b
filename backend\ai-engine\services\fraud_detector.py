"""
Fraud Detection Service
======================
خدمة كشف الاحتيال المتقدمة باستخدام الذكاء الاصطناعي
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass

import joblib
import xgboost as xgb
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import tensorflow as tf

from core.config import settings, MODEL_CONFIGS
from core.database import DatabaseManager
from core.redis_client import RedisClient
from services.model_manager import ModelManager
from services.feature_engineer import FeatureEngineer
from utils.metrics import MetricsCollector
from utils.explainer import ModelExplainer

logger = logging.getLogger(__name__)


@dataclass
class FraudPrediction:
    """نتيجة كشف الاحتيال"""
    transaction_id: str
    fraud_score: float
    risk_level: str
    confidence: float
    reasons: List[str]
    features_importance: Dict[str, float]
    timestamp: datetime
    model_version: str


@dataclass
class TransactionData:
    """بيانات المعاملة للتحليل"""
    transaction_id: str
    user_id: str
    amount: float
    currency: str
    merchant_id: str
    merchant_category: str
    timestamp: datetime
    location: Dict[str, Any]
    device_info: Dict[str, Any]
    additional_data: Dict[str, Any]


class FraudDetector:
    """كاشف الاحتيال المتقدم"""
    
    def __init__(
        self,
        model_manager: ModelManager,
        db_manager: DatabaseManager,
        redis_client: RedisClient
    ):
        self.model_manager = model_manager
        self.db_manager = db_manager
        self.redis_client = redis_client
        self.feature_engineer = FeatureEngineer()
        self.metrics_collector = MetricsCollector()
        self.explainer = ModelExplainer()
        
        # Models
        self.xgb_model = None
        self.neural_model = None
        self.isolation_forest = None
        self.scaler = None
        
        # Configuration
        self.fraud_threshold = settings.FRAUD_THRESHOLD
        self.model_version = settings.FRAUD_MODEL_VERSION
        self.feature_names = MODEL_CONFIGS["fraud_detection"]["features"]
        
        # Cache
        self.user_profiles_cache = {}
        self.merchant_profiles_cache = {}
        
        # Statistics
        self.total_predictions = 0
        self.fraud_detected = 0
        self.false_positives = 0
        
    async def initialize(self):
        """تهيئة كاشف الاحتيال"""
        try:
            logger.info("🔍 Initializing Fraud Detector...")
            
            # Load models
            await self._load_models()
            
            # Initialize feature engineer
            await self.feature_engineer.initialize()
            
            # Load user and merchant profiles
            await self._load_profiles()
            
            # Initialize explainer
            await self.explainer.initialize(self.xgb_model, self.feature_names)
            
            logger.info("✅ Fraud Detector initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Fraud Detector: {e}")
            raise
    
    async def _load_models(self):
        """تحميل نماذج كشف الاحتيال"""
        try:
            # Load XGBoost model
            xgb_path = f"{settings.MODEL_PATH}/fraud_detection_xgb_{self.model_version}.joblib"
            self.xgb_model = await self.model_manager.load_model("fraud_xgb", xgb_path)
            
            # Load Neural Network model
            nn_path = f"{settings.MODEL_PATH}/fraud_detection_nn_{self.model_version}.h5"
            self.neural_model = await self.model_manager.load_model("fraud_nn", nn_path)
            
            # Load Isolation Forest for anomaly detection
            iso_path = f"{settings.MODEL_PATH}/fraud_isolation_forest_{self.model_version}.joblib"
            self.isolation_forest = await self.model_manager.load_model("fraud_isolation", iso_path)
            
            # Load scaler
            scaler_path = f"{settings.MODEL_PATH}/fraud_scaler_{self.model_version}.joblib"
            self.scaler = await self.model_manager.load_model("fraud_scaler", scaler_path)
            
            logger.info("✅ Fraud detection models loaded")
            
        except Exception as e:
            logger.error(f"❌ Failed to load fraud detection models: {e}")
            # Create default models if not found
            await self._create_default_models()
    
    async def _create_default_models(self):
        """إنشاء نماذج افتراضية"""
        logger.info("🔧 Creating default fraud detection models...")
        
        # Create simple XGBoost model
        self.xgb_model = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
        
        # Create simple neural network
        self.neural_model = tf.keras.Sequential([
            tf.keras.layers.Dense(64, activation='relu', input_shape=(len(self.feature_names),)),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])
        
        self.neural_model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        # Create Isolation Forest
        self.isolation_forest = IsolationForest(
            contamination=0.1,
            random_state=42
        )
        
        # Create scaler
        self.scaler = StandardScaler()
        
        logger.info("✅ Default models created")
    
    async def _load_profiles(self):
        """تحميل ملفات المستخدمين والتجار"""
        try:
            # Load user profiles from cache or database
            cached_users = await self.redis_client.get("user_profiles")
            if cached_users:
                self.user_profiles_cache = json.loads(cached_users)
            else:
                await self._refresh_user_profiles()
            
            # Load merchant profiles
            cached_merchants = await self.redis_client.get("merchant_profiles")
            if cached_merchants:
                self.merchant_profiles_cache = json.loads(cached_merchants)
            else:
                await self._refresh_merchant_profiles()
                
        except Exception as e:
            logger.error(f"❌ Failed to load profiles: {e}")
    
    async def predict_fraud(self, transaction_data: TransactionData) -> FraudPrediction:
        """كشف الاحتيال في المعاملة"""
        try:
            start_time = datetime.now()
            
            # Extract features
            features = await self._extract_features(transaction_data)
            
            # Get predictions from different models
            xgb_score = await self._predict_xgb(features)
            nn_score = await self._predict_neural(features)
            anomaly_score = await self._predict_anomaly(features)
            
            # Ensemble prediction
            fraud_score = await self._ensemble_prediction(xgb_score, nn_score, anomaly_score)
            
            # Determine risk level
            risk_level = self._get_risk_level(fraud_score)
            
            # Calculate confidence
            confidence = self._calculate_confidence(xgb_score, nn_score, anomaly_score)
            
            # Get explanation
            reasons, feature_importance = await self._explain_prediction(features, fraud_score)
            
            # Create prediction result
            prediction = FraudPrediction(
                transaction_id=transaction_data.transaction_id,
                fraud_score=fraud_score,
                risk_level=risk_level,
                confidence=confidence,
                reasons=reasons,
                features_importance=feature_importance,
                timestamp=datetime.now(),
                model_version=self.model_version
            )
            
            # Update statistics
            self.total_predictions += 1
            if fraud_score >= self.fraud_threshold:
                self.fraud_detected += 1
            
            # Cache result
            await self._cache_prediction(prediction)
            
            # Log metrics
            processing_time = (datetime.now() - start_time).total_seconds()
            await self.metrics_collector.record_prediction(
                model_type="fraud_detection",
                score=fraud_score,
                processing_time=processing_time,
                risk_level=risk_level
            )
            
            logger.info(f"🔍 Fraud prediction completed: {transaction_data.transaction_id} - Score: {fraud_score:.3f}")
            
            return prediction
            
        except Exception as e:
            logger.error(f"❌ Fraud prediction failed: {e}")
            raise
    
    async def _extract_features(self, transaction_data: TransactionData) -> np.ndarray:
        """استخراج الميزات من بيانات المعاملة"""
        try:
            # Get user profile
            user_profile = await self._get_user_profile(transaction_data.user_id)
            
            # Get merchant profile
            merchant_profile = await self._get_merchant_profile(transaction_data.merchant_id)
            
            # Extract transaction features
            transaction_features = await self.feature_engineer.extract_transaction_features(
                transaction_data, user_profile, merchant_profile
            )
            
            # Extract temporal features
            temporal_features = await self.feature_engineer.extract_temporal_features(
                transaction_data.timestamp
            )
            
            # Extract behavioral features
            behavioral_features = await self.feature_engineer.extract_behavioral_features(
                transaction_data.user_id, transaction_data.timestamp
            )
            
            # Extract velocity features
            velocity_features = await self.feature_engineer.extract_velocity_features(
                transaction_data.user_id, transaction_data.timestamp
            )
            
            # Combine all features
            all_features = {
                **transaction_features,
                **temporal_features,
                **behavioral_features,
                **velocity_features
            }
            
            # Convert to array in correct order
            feature_array = np.array([all_features.get(name, 0) for name in self.feature_names])
            
            return feature_array.reshape(1, -1)
            
        except Exception as e:
            logger.error(f"❌ Feature extraction failed: {e}")
            raise
    
    async def _predict_xgb(self, features: np.ndarray) -> float:
        """التنبؤ باستخدام XGBoost"""
        try:
            if self.xgb_model is None:
                return 0.5  # Default score
            
            # Scale features
            scaled_features = self.scaler.transform(features)
            
            # Get prediction probability
            prob = self.xgb_model.predict_proba(scaled_features)[0][1]
            
            return float(prob)
            
        except Exception as e:
            logger.error(f"❌ XGBoost prediction failed: {e}")
            return 0.5
    
    async def _predict_neural(self, features: np.ndarray) -> float:
        """التنبؤ باستخدام الشبكة العصبية"""
        try:
            if self.neural_model is None:
                return 0.5  # Default score
            
            # Scale features
            scaled_features = self.scaler.transform(features)
            
            # Get prediction
            prob = self.neural_model.predict(scaled_features, verbose=0)[0][0]
            
            return float(prob)
            
        except Exception as e:
            logger.error(f"❌ Neural network prediction failed: {e}")
            return 0.5
    
    async def _predict_anomaly(self, features: np.ndarray) -> float:
        """كشف الشذوذ باستخدام Isolation Forest"""
        try:
            if self.isolation_forest is None:
                return 0.5  # Default score
            
            # Scale features
            scaled_features = self.scaler.transform(features)
            
            # Get anomaly score
            anomaly_score = self.isolation_forest.decision_function(scaled_features)[0]
            
            # Convert to probability (0-1 range)
            prob = 1 / (1 + np.exp(anomaly_score))
            
            return float(prob)
            
        except Exception as e:
            logger.error(f"❌ Anomaly detection failed: {e}")
            return 0.5
    
    async def _ensemble_prediction(self, xgb_score: float, nn_score: float, anomaly_score: float) -> float:
        """دمج التنبؤات من النماذج المختلفة"""
        # Weighted ensemble
        weights = {
            'xgb': 0.5,
            'nn': 0.3,
            'anomaly': 0.2
        }
        
        ensemble_score = (
            weights['xgb'] * xgb_score +
            weights['nn'] * nn_score +
            weights['anomaly'] * anomaly_score
        )
        
        return min(max(ensemble_score, 0.0), 1.0)
    
    def _get_risk_level(self, fraud_score: float) -> str:
        """تحديد مستوى المخاطر"""
        if fraud_score >= 0.8:
            return "very_high"
        elif fraud_score >= 0.6:
            return "high"
        elif fraud_score >= 0.4:
            return "medium"
        elif fraud_score >= 0.2:
            return "low"
        else:
            return "very_low"
    
    def _calculate_confidence(self, xgb_score: float, nn_score: float, anomaly_score: float) -> float:
        """حساب مستوى الثقة في التنبؤ"""
        # Calculate variance between models
        scores = [xgb_score, nn_score, anomaly_score]
        variance = np.var(scores)
        
        # Higher variance = lower confidence
        confidence = 1.0 - min(variance * 4, 0.5)  # Scale and cap
        
        return max(confidence, 0.5)  # Minimum confidence of 50%
    
    async def _explain_prediction(self, features: np.ndarray, fraud_score: float) -> Tuple[List[str], Dict[str, float]]:
        """شرح التنبؤ"""
        try:
            # Get feature importance from explainer
            feature_importance = await self.explainer.explain_prediction(features[0])
            
            # Generate reasons based on top features
            reasons = []
            top_features = sorted(feature_importance.items(), key=lambda x: abs(x[1]), reverse=True)[:5]
            
            for feature_name, importance in top_features:
                if importance > 0.1:
                    reasons.append(f"مؤشر مرتفع في {feature_name}")
                elif importance < -0.1:
                    reasons.append(f"مؤشر منخفض في {feature_name}")
            
            if fraud_score >= 0.8:
                reasons.append("نمط مشبوه في المعاملة")
            elif fraud_score >= 0.6:
                reasons.append("انحراف عن السلوك المعتاد")
            
            return reasons, feature_importance
            
        except Exception as e:
            logger.error(f"❌ Prediction explanation failed: {e}")
            return ["تحليل عام للمخاطر"], {}
    
    async def _get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """الحصول على ملف المستخدم"""
        if user_id in self.user_profiles_cache:
            return self.user_profiles_cache[user_id]
        
        # Load from database
        profile = await self.db_manager.get_user_profile(user_id)
        if profile:
            self.user_profiles_cache[user_id] = profile
            return profile
        
        # Return default profile
        return {
            "age": 30,
            "account_age_days": 365,
            "kyc_level": 1,
            "avg_transaction_amount": 1000,
            "transaction_count": 10
        }
    
    async def _get_merchant_profile(self, merchant_id: str) -> Dict[str, Any]:
        """الحصول على ملف التاجر"""
        if merchant_id in self.merchant_profiles_cache:
            return self.merchant_profiles_cache[merchant_id]
        
        # Load from database
        profile = await self.db_manager.get_merchant_profile(merchant_id)
        if profile:
            self.merchant_profiles_cache[merchant_id] = profile
            return profile
        
        # Return default profile
        return {
            "risk_category": "medium",
            "transaction_volume": 1000000,
            "chargeback_rate": 0.01,
            "reputation_score": 0.8
        }
    
    async def _cache_prediction(self, prediction: FraudPrediction):
        """حفظ التنبؤ في الكاش"""
        try:
            cache_key = f"fraud_prediction:{prediction.transaction_id}"
            cache_data = {
                "fraud_score": prediction.fraud_score,
                "risk_level": prediction.risk_level,
                "confidence": prediction.confidence,
                "timestamp": prediction.timestamp.isoformat(),
                "model_version": prediction.model_version
            }
            
            await self.redis_client.setex(
                cache_key,
                3600,  # 1 hour TTL
                json.dumps(cache_data)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to cache prediction: {e}")
    
    async def _refresh_user_profiles(self):
        """تحديث ملفات المستخدمين"""
        try:
            profiles = await self.db_manager.get_all_user_profiles()
            self.user_profiles_cache = profiles
            
            # Cache in Redis
            await self.redis_client.setex(
                "user_profiles",
                3600,  # 1 hour TTL
                json.dumps(profiles)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to refresh user profiles: {e}")
    
    async def _refresh_merchant_profiles(self):
        """تحديث ملفات التجار"""
        try:
            profiles = await self.db_manager.get_all_merchant_profiles()
            self.merchant_profiles_cache = profiles
            
            # Cache in Redis
            await self.redis_client.setex(
                "merchant_profiles",
                3600,  # 1 hour TTL
                json.dumps(profiles)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to refresh merchant profiles: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات كاشف الاحتيال"""
        return {
            "total_predictions": self.total_predictions,
            "fraud_detected": self.fraud_detected,
            "fraud_rate": self.fraud_detected / max(self.total_predictions, 1),
            "false_positives": self.false_positives,
            "model_version": self.model_version,
            "fraud_threshold": self.fraud_threshold,
            "cache_size": {
                "users": len(self.user_profiles_cache),
                "merchants": len(self.merchant_profiles_cache)
            }
        }
    
    async def cleanup(self):
        """تنظيف الموارد"""
        try:
            logger.info("🧹 Cleaning up Fraud Detector...")
            
            # Clear caches
            self.user_profiles_cache.clear()
            self.merchant_profiles_cache.clear()
            
            # Cleanup models
            if self.neural_model:
                del self.neural_model
            
            logger.info("✅ Fraud Detector cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Fraud Detector cleanup failed: {e}")
