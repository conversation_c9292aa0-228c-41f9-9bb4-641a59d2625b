"""
Configuration settings for AI Engine
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings"""
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "WS Transfir AI Engine"
    VERSION: str = "1.0.0"
    
    # Server Settings
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 4
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # Database Settings
    POSTGRES_URL: str = "postgresql://ws_user:ws_password@localhost:5432/ws_transfir"
    MONGODB_URL: str = "**************************************************************"
    REDIS_URL: str = "redis://localhost:6379"
    ELASTICSEARCH_URL: str = "http://localhost:9200"
    
    # Database Pool Settings
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    
    # Redis Settings
    REDIS_POOL_SIZE: int = 10
    REDIS_TIMEOUT: int = 5
    REDIS_RETRY_ON_TIMEOUT: bool = True
    
    # Model Settings
    MODEL_PATH: str = "./models"
    MODEL_CACHE_SIZE: int = 5
    MODEL_RELOAD_INTERVAL: int = 3600  # 1 hour
    
    # Fraud Detection Settings
    FRAUD_THRESHOLD: float = 0.7
    FRAUD_MODEL_VERSION: str = "v1.2.0"
    FRAUD_FEATURES_COUNT: int = 50
    
    # Risk Assessment Settings
    RISK_THRESHOLD_LOW: float = 0.3
    RISK_THRESHOLD_MEDIUM: float = 0.6
    RISK_THRESHOLD_HIGH: float = 0.8
    RISK_MODEL_VERSION: str = "v1.1.0"
    
    # Recommendation Settings
    RECOMMENDATION_COUNT: int = 10
    RECOMMENDATION_THRESHOLD: float = 0.5
    RECOMMENDATION_MODEL_VERSION: str = "v1.0.0"
    
    # ML Settings
    ML_BATCH_SIZE: int = 1000
    ML_LEARNING_RATE: float = 0.001
    ML_EPOCHS: int = 100
    ML_VALIDATION_SPLIT: float = 0.2
    
    # Feature Engineering
    FEATURE_WINDOW_DAYS: int = 30
    FEATURE_AGGREGATION_PERIODS: List[str] = ["1d", "7d", "30d"]
    
    # Monitoring Settings
    METRICS_ENABLED: bool = True
    METRICS_PORT: int = 9090
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # Performance Settings
    MAX_CONCURRENT_REQUESTS: int = 100
    REQUEST_TIMEOUT: int = 30
    CACHE_TTL: int = 3600
    
    # Data Processing
    DATA_BATCH_SIZE: int = 10000
    DATA_PROCESSING_WORKERS: int = 4
    DATA_RETENTION_DAYS: int = 365
    
    # External APIs
    EXCHANGE_RATE_API_KEY: Optional[str] = None
    EXCHANGE_RATE_API_URL: str = "https://api.exchangerate-api.com/v4/latest"
    
    # Notification Settings
    ALERT_WEBHOOK_URL: Optional[str] = None
    SLACK_WEBHOOK_URL: Optional[str] = None
    
    # File Storage
    UPLOAD_PATH: str = "./uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = [".csv", ".json", ".xlsx"]
    
    # Backup Settings
    BACKUP_ENABLED: bool = True
    BACKUP_INTERVAL_HOURS: int = 24
    BACKUP_RETENTION_DAYS: int = 30
    
    # Feature Flags
    ENABLE_REAL_TIME_SCORING: bool = True
    ENABLE_BATCH_PROCESSING: bool = True
    ENABLE_MODEL_RETRAINING: bool = True
    ENABLE_EXPLAINABLE_AI: bool = True
    ENABLE_A_B_TESTING: bool = False
    
    # Localization
    DEFAULT_LANGUAGE: str = "ar"
    SUPPORTED_LANGUAGES: List[str] = ["ar", "en"]
    DEFAULT_TIMEZONE: str = "Asia/Riyadh"
    
    # Currency Settings
    DEFAULT_CURRENCY: str = "SAR"
    SUPPORTED_CURRENCIES: List[str] = ["SAR", "USD", "EUR", "GBP"]
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # seconds
    
    # Health Check Settings
    HEALTH_CHECK_INTERVAL: int = 30
    HEALTH_CHECK_TIMEOUT: int = 10
    HEALTH_CHECK_RETRIES: int = 3
    
    # Model Training Settings
    TRAINING_DATA_MIN_SIZE: int = 10000
    TRAINING_VALIDATION_SPLIT: float = 0.2
    TRAINING_TEST_SPLIT: float = 0.1
    TRAINING_EARLY_STOPPING_PATIENCE: int = 10
    
    # Anomaly Detection Settings
    ANOMALY_THRESHOLD: float = 0.8
    ANOMALY_WINDOW_SIZE: int = 100
    ANOMALY_MIN_SAMPLES: int = 50
    
    # Clustering Settings
    CLUSTERING_MIN_CLUSTER_SIZE: int = 10
    CLUSTERING_MAX_CLUSTERS: int = 20
    CLUSTERING_ALGORITHM: str = "kmeans"
    
    # Time Series Settings
    TIME_SERIES_WINDOW: int = 30
    TIME_SERIES_FORECAST_HORIZON: int = 7
    TIME_SERIES_SEASONALITY: bool = True
    
    # Model Serving Settings
    MODEL_SERVING_BATCH_SIZE: int = 32
    MODEL_SERVING_MAX_LATENCY: int = 1000  # milliseconds
    MODEL_SERVING_WORKERS: int = 2
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        if v not in ["development", "staging", "production"]:
            raise ValueError("Environment must be development, staging, or production")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        if v not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError("Invalid log level")
        return v
    
    @validator("FRAUD_THRESHOLD")
    def validate_fraud_threshold(cls, v):
        if not 0 <= v <= 1:
            raise ValueError("Fraud threshold must be between 0 and 1")
        return v
    
    @validator("POSTGRES_URL")
    def validate_postgres_url(cls, v):
        if not v.startswith("postgresql://"):
            raise ValueError("Invalid PostgreSQL URL")
        return v
    
    @validator("REDIS_URL")
    def validate_redis_url(cls, v):
        if not v.startswith("redis://"):
            raise ValueError("Invalid Redis URL")
        return v
    
    @property
    def is_development(self) -> bool:
        return self.ENVIRONMENT == "development"
    
    @property
    def is_production(self) -> bool:
        return self.ENVIRONMENT == "production"
    
    @property
    def database_url(self) -> str:
        return self.POSTGRES_URL
    
    @property
    def mongodb_url(self) -> str:
        return self.MONGODB_URL
    
    @property
    def redis_url(self) -> str:
        return self.REDIS_URL
    
    @property
    def elasticsearch_url(self) -> str:
        return self.ELASTICSEARCH_URL
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Global settings instance
settings = get_settings()


# Model configurations
MODEL_CONFIGS = {
    "fraud_detection": {
        "model_type": "xgboost",
        "features": [
            "amount", "hour", "day_of_week", "merchant_category",
            "user_age", "account_age", "transaction_frequency",
            "amount_percentile", "velocity_1h", "velocity_24h"
        ],
        "hyperparameters": {
            "n_estimators": 100,
            "max_depth": 6,
            "learning_rate": 0.1,
            "subsample": 0.8
        }
    },
    "risk_assessment": {
        "model_type": "random_forest",
        "features": [
            "credit_score", "income", "debt_ratio", "employment_length",
            "transaction_history", "kyc_level", "country_risk"
        ],
        "hyperparameters": {
            "n_estimators": 200,
            "max_depth": 10,
            "min_samples_split": 5,
            "min_samples_leaf": 2
        }
    },
    "recommendations": {
        "model_type": "collaborative_filtering",
        "features": [
            "user_preferences", "transaction_patterns", "demographic_data",
            "seasonal_trends", "product_affinity"
        ],
        "hyperparameters": {
            "n_factors": 50,
            "n_epochs": 20,
            "lr_all": 0.005,
            "reg_all": 0.02
        }
    }
}


# Feature engineering configurations
FEATURE_CONFIGS = {
    "transaction_features": {
        "amount_features": ["log_amount", "amount_zscore", "amount_percentile"],
        "time_features": ["hour", "day_of_week", "month", "is_weekend"],
        "velocity_features": ["velocity_1h", "velocity_24h", "velocity_7d"],
        "frequency_features": ["daily_frequency", "weekly_frequency", "monthly_frequency"]
    },
    "user_features": {
        "demographic": ["age", "gender", "location", "income_bracket"],
        "behavioral": ["avg_transaction_amount", "preferred_time", "channel_preference"],
        "risk_indicators": ["kyc_level", "account_age", "complaint_count"]
    },
    "merchant_features": {
        "category": ["mcc_code", "business_type", "risk_category"],
        "reputation": ["rating", "transaction_volume", "chargeback_rate"],
        "location": ["country", "city", "high_risk_area"]
    }
}
