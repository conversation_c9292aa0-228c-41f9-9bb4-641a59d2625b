name: integration_test_macos
description: Desktop implementation of integration_test plugin
version: 0.0.1+1
homepage: https://github.com/flutter/flutter/tree/main/packages/integration_test/integration_test_macos

flutter:
  plugin:
    platforms:
      macos:
        pluginClass: IntegrationTestPlugin

environment:
  sdk: ^3.7.0-0

dependencies:
  flutter:
    sdk: flutter

  characters: 1.4.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  collection: 1.19.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  material_color_utilities: 0.11.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  meta: 1.16.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  vector_math: 2.1.4 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"

dev_dependencies:
  pedantic: 1.11.1

# PUBSPEC CHECKSUM: de8e
