"""
Integration API
==============
واجهة برمجة التطبيقات للتكامل والواجهات الخارجية
"""

import asyncio
import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal

from fastapi import APIRouter, HTTPException, Depends, Query, Body, Path
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
import jwt

from .bank_integration_service import (
    BankIntegrationService, BankTransferRequest, BankAccount, 
    TransactionStatus, BankType
)
from .partner_api_service import (
    PartnerAPIService, PartnerConfig, PartnerType, APIVersion,
    AuthMethod, RateLimit, APIRequest
)
from .webhook_service import (
    WebhookService, WebhookEndpoint, WebhookEvent, 
    SignatureMethod, WebhookStatus
)
from .accounting_integration_service import (
    AccountingIntegrationService, AccountingSystem, TransactionType,
    TaxType, InvoiceData
)
from .industry_standards_service import (
    IndustryStandardsService, IndustryStandard, MessageFormat,
    ComplianceLevel
)
from ..shared.database.connection import DatabaseConnection

logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Router
router = APIRouter(prefix="/api/v1/integration", tags=["Integration"])


# Pydantic Models
class BankTransferRequestModel(BaseModel):
    """نموذج طلب التحويل البنكي"""
    from_account: str = Field(..., description="حساب المرسل")
    to_account: str = Field(..., description="حساب المستقبل")
    amount: Decimal = Field(..., gt=0, description="المبلغ")
    currency: str = Field(default="SAR", description="العملة")
    reference: str = Field(..., description="المرجع")
    description: str = Field(..., description="الوصف")
    beneficiary_name: str = Field(..., description="اسم المستفيد")
    beneficiary_bank: str = Field(..., description="بنك المستفيد")
    purpose_code: Optional[str] = Field(None, description="رمز الغرض")
    metadata: Optional[Dict[str, Any]] = Field(None, description="بيانات إضافية")


class BankAccountModel(BaseModel):
    """نموذج الحساب البنكي"""
    account_number: str = Field(..., description="رقم الحساب")
    iban: str = Field(..., description="IBAN")
    bank_code: str = Field(..., description="رمز البنك")
    bank_name: str = Field(..., description="اسم البنك")
    account_holder: str = Field(..., description="صاحب الحساب")
    currency: str = Field(default="SAR", description="العملة")
    account_type: str = Field(..., description="نوع الحساب")
    is_active: bool = Field(default=True, description="نشط")


class PartnerConfigModel(BaseModel):
    """نموذج إعدادات الشريك"""
    partner_name: str = Field(..., description="اسم الشريك")
    partner_type: PartnerType = Field(..., description="نوع الشريك")
    api_version: APIVersion = Field(..., description="إصدار API")
    auth_method: AuthMethod = Field(..., description="طريقة المصادقة")
    rate_limit: RateLimit = Field(..., description="حد المعدل")
    webhook_url: Optional[str] = Field(None, description="رابط Webhook")
    allowed_ips: Optional[List[str]] = Field(None, description="عناوين IP المسموحة")
    metadata: Optional[Dict[str, Any]] = Field(None, description="بيانات إضافية")


class WebhookEndpointModel(BaseModel):
    """نموذج نقطة نهاية Webhook"""
    partner_id: str = Field(..., description="معرف الشريك")
    url: str = Field(..., description="رابط النقطة")
    events: List[WebhookEvent] = Field(..., description="الأحداث")
    secret_key: str = Field(..., description="المفتاح السري")
    signature_method: SignatureMethod = Field(default=SignatureMethod.HMAC_SHA256, description="طريقة التوقيع")
    max_retries: int = Field(default=3, ge=0, le=10, description="عدد المحاولات")
    retry_delay: int = Field(default=60, ge=10, le=3600, description="تأخير المحاولة")
    timeout: int = Field(default=30, ge=5, le=300, description="انتهاء الوقت")
    metadata: Optional[Dict[str, Any]] = Field(None, description="بيانات إضافية")


class InvoiceLineItem(BaseModel):
    """عنصر الفاتورة"""
    description: str = Field(..., description="الوصف")
    quantity: Decimal = Field(..., gt=0, description="الكمية")
    unit_price: Decimal = Field(..., gt=0, description="سعر الوحدة")
    amount: Decimal = Field(..., gt=0, description="المبلغ")
    taxable: bool = Field(default=True, description="خاضع للضريبة")


class InvoiceRequestModel(BaseModel):
    """نموذج طلب الفاتورة"""
    customer_id: str = Field(..., description="معرف العميل")
    line_items: List[InvoiceLineItem] = Field(..., description="عناصر الفاتورة")
    payment_terms: str = Field(default="Net 30", description="شروط الدفع")
    notes: Optional[str] = Field(None, description="ملاحظات")


class MessageValidationRequest(BaseModel):
    """نموذج طلب التحقق من الرسالة"""
    message_type: str = Field(..., description="نوع الرسالة")
    message_content: str = Field(..., description="محتوى الرسالة")
    format: MessageFormat = Field(..., description="تنسيق الرسالة")


# Dependency injection
async def get_bank_service() -> BankIntegrationService:
    """الحصول على خدمة التكامل البنكي"""
    db_connection = DatabaseConnection()
    return BankIntegrationService(db_connection)


async def get_partner_service() -> PartnerAPIService:
    """الحصول على خدمة API الشركاء"""
    db_connection = DatabaseConnection()
    return PartnerAPIService(db_connection)


async def get_webhook_service() -> WebhookService:
    """الحصول على خدمة Webhook"""
    db_connection = DatabaseConnection()
    service = WebhookService(db_connection)
    await service.initialize()
    return service


async def get_accounting_service() -> AccountingIntegrationService:
    """الحصول على خدمة التكامل المحاسبي"""
    db_connection = DatabaseConnection()
    service = AccountingIntegrationService(db_connection)
    await service.initialize()
    return service


async def get_standards_service() -> IndustryStandardsService:
    """الحصول على خدمة معايير الصناعة"""
    db_connection = DatabaseConnection()
    service = IndustryStandardsService(db_connection)
    await service.initialize()
    return service


async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """التحقق من الرمز المميز"""
    try:
        # In production, use proper JWT verification
        payload = jwt.decode(
            credentials.credentials, 
            "your_secret_key", 
            algorithms=["HS256"]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")


# Bank Integration Endpoints
@router.post("/bank/transfer", response_model=Dict[str, Any])
async def initiate_bank_transfer(
    request: BankTransferRequestModel,
    bank_service: BankIntegrationService = Depends(get_bank_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """بدء تحويل بنكي"""
    try:
        # Convert to service model
        transfer_request = BankTransferRequest(
            from_account=request.from_account,
            to_account=request.to_account,
            amount=request.amount,
            currency=request.currency,
            reference=request.reference,
            description=request.description,
            beneficiary_name=request.beneficiary_name,
            beneficiary_bank=request.beneficiary_bank,
            purpose_code=request.purpose_code,
            metadata=request.metadata
        )
        
        # Initiate transfer
        result = await bank_service.initiate_bank_transfer(transfer_request)
        
        return {
            "success": True,
            "transfer_id": result.transfer_id,
            "status": result.status.value,
            "bank_reference": result.bank_reference,
            "amount": float(result.amount),
            "currency": result.currency,
            "fees": float(result.fees_amount) if result.fees_amount else 0.0,
            "estimated_completion": result.estimated_completion.isoformat() if result.estimated_completion else None,
            "created_at": result.created_at.isoformat() if result.created_at else None
        }
        
    except Exception as e:
        logger.error(f"❌ Bank transfer failed: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/bank/balance/{account_number}", response_model=Dict[str, Any])
async def get_account_balance(
    account_number: str = Path(..., description="رقم الحساب"),
    bank_code: Optional[str] = Query(None, description="رمز البنك"),
    bank_service: BankIntegrationService = Depends(get_bank_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """الحصول على رصيد الحساب"""
    try:
        balance = await bank_service.get_account_balance(account_number, bank_code)
        
        return {
            "success": True,
            "account_number": balance.account_number,
            "available_balance": float(balance.available_balance),
            "current_balance": float(balance.current_balance),
            "pending_balance": float(balance.pending_balance),
            "currency": balance.currency,
            "last_updated": balance.last_updated.isoformat(),
            "bank_reference": balance.bank_reference
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get account balance: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/bank/account", response_model=Dict[str, Any])
async def register_bank_account(
    account: BankAccountModel,
    bank_service: BankIntegrationService = Depends(get_bank_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """تسجيل حساب بنكي"""
    try:
        # Convert to service model
        bank_account = BankAccount(
            account_number=account.account_number,
            iban=account.iban,
            bank_code=account.bank_code,
            bank_name=account.bank_name,
            account_holder=account.account_holder,
            currency=account.currency,
            account_type=account.account_type,
            is_active=account.is_active
        )
        
        # Register account
        success = await bank_service.register_bank_account(bank_account)
        
        return {
            "success": success,
            "message": "Bank account registered successfully" if success else "Failed to register bank account"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to register bank account: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/bank/supported", response_model=List[Dict[str, Any]])
async def get_supported_banks(
    bank_service: BankIntegrationService = Depends(get_bank_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """الحصول على البنوك المدعومة"""
    try:
        banks = await bank_service.get_supported_banks()
        return banks
        
    except Exception as e:
        logger.error(f"❌ Failed to get supported banks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Partner API Endpoints
@router.post("/partner/register", response_model=Dict[str, Any])
async def register_partner(
    config: PartnerConfigModel,
    partner_service: PartnerAPIService = Depends(get_partner_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """تسجيل شريك جديد"""
    try:
        # Generate partner ID
        partner_id = f"partner_{datetime.now().strftime('%Y%m%d')}_{hash(config.partner_name) % 10000:04d}"
        
        # Convert to service model
        partner_config = PartnerConfig(
            partner_id=partner_id,
            partner_name=config.partner_name,
            partner_type=config.partner_type,
            api_version=config.api_version,
            auth_method=config.auth_method,
            rate_limit=config.rate_limit,
            webhook_url=config.webhook_url,
            allowed_ips=config.allowed_ips,
            metadata=config.metadata
        )
        
        # Register partner
        success = await partner_service.register_partner(partner_config)
        
        if success:
            return {
                "success": True,
                "partner_id": partner_id,
                "api_key": partner_config.api_key,
                "secret_key": partner_config.secret_key,
                "message": "Partner registered successfully"
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to register partner")
        
    except Exception as e:
        logger.error(f"❌ Failed to register partner: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/partner/{partner_id}/analytics", response_model=Dict[str, Any])
async def get_partner_analytics(
    partner_id: str = Path(..., description="معرف الشريك"),
    start_date: Optional[datetime] = Query(None, description="تاريخ البداية"),
    end_date: Optional[datetime] = Query(None, description="تاريخ النهاية"),
    partner_service: PartnerAPIService = Depends(get_partner_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """الحصول على تحليلات الشريك"""
    try:
        analytics = await partner_service.get_partner_analytics(partner_id, start_date, end_date)
        return analytics
        
    except Exception as e:
        logger.error(f"❌ Failed to get partner analytics: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/partner/list", response_model=List[Dict[str, Any]])
async def list_partners(
    active_only: bool = Query(True, description="النشطون فقط"),
    partner_service: PartnerAPIService = Depends(get_partner_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """قائمة الشركاء"""
    try:
        partners = await partner_service.get_all_partners(active_only)
        return partners
        
    except Exception as e:
        logger.error(f"❌ Failed to list partners: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Webhook Endpoints
@router.post("/webhook/endpoint", response_model=Dict[str, Any])
async def register_webhook_endpoint(
    endpoint: WebhookEndpointModel,
    webhook_service: WebhookService = Depends(get_webhook_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """تسجيل نقطة نهاية webhook"""
    try:
        # Generate endpoint ID
        endpoint_id = f"wh_{datetime.now().strftime('%Y%m%d')}_{hash(endpoint.url) % 10000:04d}"
        
        # Convert to service model
        webhook_endpoint = WebhookEndpoint(
            endpoint_id=endpoint_id,
            partner_id=endpoint.partner_id,
            url=endpoint.url,
            events=endpoint.events,
            secret_key=endpoint.secret_key,
            signature_method=endpoint.signature_method,
            max_retries=endpoint.max_retries,
            retry_delay=endpoint.retry_delay,
            timeout=endpoint.timeout,
            metadata=endpoint.metadata
        )
        
        # Register endpoint
        success = await webhook_service.register_endpoint(webhook_endpoint)
        
        if success:
            return {
                "success": True,
                "endpoint_id": endpoint_id,
                "message": "Webhook endpoint registered successfully"
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to register webhook endpoint")
        
    except Exception as e:
        logger.error(f"❌ Failed to register webhook endpoint: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/webhook/send", response_model=Dict[str, Any])
async def send_webhook(
    partner_id: str = Body(..., description="معرف الشريك"),
    event: WebhookEvent = Body(..., description="الحدث"),
    data: Dict[str, Any] = Body(..., description="البيانات"),
    webhook_service: WebhookService = Depends(get_webhook_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """إرسال webhook"""
    try:
        delivery_ids = await webhook_service.send_webhook(partner_id, event, data)
        
        return {
            "success": True,
            "delivery_ids": delivery_ids,
            "message": f"Webhook sent to {len(delivery_ids)} endpoints"
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to send webhook: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/webhook/delivery/{delivery_id}", response_model=Dict[str, Any])
async def get_webhook_delivery_status(
    delivery_id: str = Path(..., description="معرف التسليم"),
    webhook_service: WebhookService = Depends(get_webhook_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """الحصول على حالة تسليم webhook"""
    try:
        delivery = await webhook_service.get_delivery_status(delivery_id)
        
        if not delivery:
            raise HTTPException(status_code=404, detail="Delivery not found")
        
        return {
            "success": True,
            "delivery_id": delivery.delivery_id,
            "webhook_id": delivery.webhook_id,
            "partner_id": delivery.partner_id,
            "event": delivery.event.value,
            "status": delivery.status.value,
            "attempts": delivery.attempts,
            "max_retries": delivery.max_retries,
            "response_status": delivery.response_status,
            "error_message": delivery.error_message,
            "created_at": delivery.created_at.isoformat() if delivery.created_at else None,
            "delivered_at": delivery.delivered_at.isoformat() if delivery.delivered_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get webhook delivery status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Accounting Integration Endpoints
@router.post("/accounting/invoice", response_model=Dict[str, Any])
async def generate_invoice(
    request: InvoiceRequestModel,
    accounting_service: AccountingIntegrationService = Depends(get_accounting_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """إنشاء فاتورة"""
    try:
        # Convert line items
        line_items = [
            {
                "description": item.description,
                "quantity": float(item.quantity),
                "unit_price": float(item.unit_price),
                "amount": float(item.amount),
                "taxable": item.taxable
            }
            for item in request.line_items
        ]
        
        # Generate invoice
        invoice = await accounting_service.generate_invoice(
            request.customer_id,
            line_items,
            request.payment_terms,
            request.notes
        )
        
        return {
            "success": True,
            "invoice_id": invoice.invoice_id,
            "invoice_number": invoice.invoice_number,
            "customer_name": invoice.customer_name,
            "subtotal": float(invoice.subtotal),
            "tax_amount": float(invoice.tax_amount),
            "total_amount": float(invoice.total_amount),
            "currency": invoice.currency,
            "issue_date": invoice.issue_date.isoformat(),
            "due_date": invoice.due_date.isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to generate invoice: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/accounting/zatca/submit/{invoice_id}", response_model=Dict[str, Any])
async def submit_invoice_to_zatca(
    invoice_id: str = Path(..., description="معرف الفاتورة"),
    accounting_service: AccountingIntegrationService = Depends(get_accounting_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """تقديم الفاتورة لهيئة الزكاة والضريبة والجمارك"""
    try:
        # Get invoice data (simplified - in production, retrieve from database)
        # For now, create a mock invoice
        from .accounting_integration_service import InvoiceData
        
        mock_invoice = InvoiceData(
            invoice_id=invoice_id,
            invoice_number=f"INV-{invoice_id[-6:]}",
            customer_id="customer_001",
            customer_name="Test Customer",
            customer_vat_number="***************",
            issue_date=date.today(),
            due_date=date.today() + timedelta(days=30),
            subtotal=Decimal('1000.00'),
            tax_amount=Decimal('150.00'),
            total_amount=Decimal('1150.00'),
            currency="SAR",
            line_items=[
                {
                    "description": "Service fee",
                    "quantity": 1,
                    "unit_price": 1000.00,
                    "amount": 1000.00,
                    "taxable": True,
                    "tax_amount": 150.00
                }
            ]
        )
        
        # Submit to ZATCA
        result = await accounting_service.submit_to_zatca(mock_invoice)
        
        return {
            "success": result.get("status") == "accepted",
            "zatca_reference": result.get("zatca_reference"),
            "invoice_hash": result.get("invoice_hash"),
            "qr_code": result.get("qr_code"),
            "submission_time": result.get("submission_time"),
            "message": "Invoice submitted to ZATCA successfully" if result.get("status") == "accepted" else result.get("error", "Submission failed")
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to submit invoice to ZATCA: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/accounting/summary", response_model=Dict[str, Any])
async def get_financial_summary(
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    accounting_service: AccountingIntegrationService = Depends(get_accounting_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """الحصول على الملخص المالي"""
    try:
        summary = await accounting_service.get_financial_summary(start_date, end_date)
        return summary
        
    except Exception as e:
        logger.error(f"❌ Failed to get financial summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Industry Standards Endpoints
@router.post("/standards/validate/iso20022", response_model=Dict[str, Any])
async def validate_iso20022_message(
    request: MessageValidationRequest,
    standards_service: IndustryStandardsService = Depends(get_standards_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """التحقق من صحة رسالة ISO 20022"""
    try:
        validation = await standards_service.validate_iso20022_message(
            request.message_type,
            request.message_content
        )
        
        return {
            "success": True,
            "validation_id": validation.validation_id,
            "message_type": validation.message_type,
            "format": validation.format.value,
            "is_valid": validation.is_valid,
            "errors": validation.errors,
            "warnings": validation.warnings,
            "validated_at": validation.validated_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to validate ISO 20022 message: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/standards/compliance/pci", response_model=Dict[str, Any])
async def perform_pci_compliance_check(
    standards_service: IndustryStandardsService = Depends(get_standards_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """إجراء فحص امتثال PCI DSS"""
    try:
        checks = await standards_service.perform_pci_dss_compliance_check()
        
        return {
            "success": True,
            "total_checks": len(checks),
            "passed_checks": len([c for c in checks if c.status == "passed"]),
            "failed_checks": len([c for c in checks if c.status == "failed"]),
            "warning_checks": len([c for c in checks if c.status == "warning"]),
            "compliance_rate": (len([c for c in checks if c.status == "passed"]) / max(len(checks), 1)) * 100,
            "checks": [
                {
                    "check_id": check.check_id,
                    "rule_name": check.rule_name,
                    "description": check.description,
                    "severity": check.severity,
                    "status": check.status,
                    "details": check.details,
                    "checked_at": check.checked_at.isoformat()
                }
                for check in checks
            ]
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to perform PCI compliance check: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/standards/supported", response_model=List[Dict[str, Any]])
async def get_supported_standards(
    standards_service: IndustryStandardsService = Depends(get_standards_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """الحصول على المعايير المدعومة"""
    try:
        standards = await standards_service.get_supported_standards()
        return standards
        
    except Exception as e:
        logger.error(f"❌ Failed to get supported standards: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Statistics Endpoints
@router.get("/statistics", response_model=Dict[str, Any])
async def get_integration_statistics(
    bank_service: BankIntegrationService = Depends(get_bank_service),
    partner_service: PartnerAPIService = Depends(get_partner_service),
    webhook_service: WebhookService = Depends(get_webhook_service),
    accounting_service: AccountingIntegrationService = Depends(get_accounting_service),
    standards_service: IndustryStandardsService = Depends(get_standards_service),
    current_user: Dict[str, Any] = Depends(verify_token)
):
    """الحصول على إحصائيات التكامل"""
    try:
        # Get statistics from all services
        bank_stats = await bank_service.get_service_statistics()
        partner_stats = await partner_service.get_service_statistics()
        webhook_stats = await webhook_service.get_service_statistics()
        accounting_stats = await accounting_service.get_service_statistics()
        standards_stats = await standards_service.get_service_statistics()
        
        return {
            "success": True,
            "timestamp": datetime.now().isoformat(),
            "bank_integration": bank_stats,
            "partner_api": partner_stats,
            "webhooks": webhook_stats,
            "accounting": accounting_stats,
            "industry_standards": standards_stats
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get integration statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))
