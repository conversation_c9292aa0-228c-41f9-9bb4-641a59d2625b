"""
Recommendation Engine Service
============================
محرك التوصيات الذكية المتقدم
"""

import asyncio
import json
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
from enum import Enum

import joblib
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import NMF
from sklearn.cluster import KMeans
import tensorflow as tf

from core.config import settings, MODEL_CONFIGS
from core.database import DatabaseManager
from core.redis_client import RedisClient
from services.model_manager import ModelManager
from services.feature_engineer import FeatureEngineer
from utils.metrics import MetricsCollector

logger = logging.getLogger(__name__)


class RecommendationType(Enum):
    """أنواع التوصيات"""
    PRODUCT = "product"
    SERVICE = "service"
    PROMOTION = "promotion"
    FINANCIAL_ADVICE = "financial_advice"
    SECURITY_TIP = "security_tip"
    FEATURE = "feature"


class RecommendationPriority(Enum):
    """أولوية التوصيات"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class Recommendation:
    """توصية ذكية"""
    id: str
    user_id: str
    type: RecommendationType
    title: str
    description: str
    priority: RecommendationPriority
    confidence_score: float
    relevance_score: float
    expected_value: float
    category: str
    tags: List[str]
    action_url: Optional[str]
    expires_at: Optional[datetime]
    created_at: datetime
    metadata: Dict[str, Any]


@dataclass
class UserPreferences:
    """تفضيلات المستخدم"""
    user_id: str
    preferred_categories: List[str]
    preferred_languages: List[str]
    preferred_channels: List[str]
    risk_tolerance: str
    investment_goals: List[str]
    communication_frequency: str
    interests: List[str]
    demographics: Dict[str, Any]


class RecommendationEngine:
    """محرك التوصيات الذكية"""
    
    def __init__(
        self,
        model_manager: ModelManager,
        db_manager: DatabaseManager,
        redis_client: RedisClient
    ):
        self.model_manager = model_manager
        self.db_manager = db_manager
        self.redis_client = redis_client
        self.feature_engineer = FeatureEngineer()
        self.metrics_collector = MetricsCollector()
        
        # Models
        self.collaborative_model = None
        self.content_based_model = None
        self.deep_learning_model = None
        self.clustering_model = None
        
        # Configuration
        self.recommendation_count = settings.RECOMMENDATION_COUNT
        self.confidence_threshold = settings.RECOMMENDATION_THRESHOLD
        self.model_version = settings.RECOMMENDATION_MODEL_VERSION
        
        # Data
        self.user_item_matrix = None
        self.item_features = None
        self.user_features = None
        self.user_clusters = {}
        
        # Cache
        self.user_preferences_cache = {}
        self.recommendations_cache = {}
        self.popular_items_cache = []
        
        # Statistics
        self.total_recommendations = 0
        self.successful_recommendations = 0
        
    async def initialize(self):
        """تهيئة محرك التوصيات"""
        try:
            logger.info("🎯 Initializing Recommendation Engine...")
            
            # Load models
            await self._load_models()
            
            # Initialize feature engineer
            await self.feature_engineer.initialize()
            
            # Load recommendation data
            await self._load_recommendation_data()
            
            # Build user-item matrix
            await self._build_user_item_matrix()
            
            # Perform user clustering
            await self._perform_user_clustering()
            
            logger.info("✅ Recommendation Engine initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Recommendation Engine: {e}")
            raise
    
    async def _load_models(self):
        """تحميل نماذج التوصيات"""
        try:
            # Load Collaborative Filtering model (NMF)
            cf_path = f"{settings.MODEL_PATH}/recommendation_cf_{self.model_version}.joblib"
            self.collaborative_model = await self.model_manager.load_model("recommendation_cf", cf_path)
            
            # Load Content-Based model
            cb_path = f"{settings.MODEL_PATH}/recommendation_cb_{self.model_version}.joblib"
            self.content_based_model = await self.model_manager.load_model("recommendation_cb", cb_path)
            
            # Load Deep Learning model
            dl_path = f"{settings.MODEL_PATH}/recommendation_dl_{self.model_version}.h5"
            self.deep_learning_model = await self.model_manager.load_model("recommendation_dl", dl_path)
            
            # Load Clustering model
            cluster_path = f"{settings.MODEL_PATH}/user_clustering_{self.model_version}.joblib"
            self.clustering_model = await self.model_manager.load_model("user_clustering", cluster_path)
            
            logger.info("✅ Recommendation models loaded")
            
        except Exception as e:
            logger.error(f"❌ Failed to load recommendation models: {e}")
            await self._create_default_models()
    
    async def _create_default_models(self):
        """إنشاء نماذج افتراضية"""
        logger.info("🔧 Creating default recommendation models...")
        
        # Create NMF model for collaborative filtering
        self.collaborative_model = NMF(
            n_components=50,
            init='random',
            random_state=42,
            max_iter=200
        )
        
        # Create clustering model
        self.clustering_model = KMeans(
            n_clusters=10,
            random_state=42,
            n_init=10
        )
        
        # Create deep learning model
        self.deep_learning_model = tf.keras.Sequential([
            tf.keras.layers.Dense(128, activation='relu', input_shape=(100,)),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(64, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dense(10, activation='softmax')  # 10 recommendation categories
        ])
        
        self.deep_learning_model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        logger.info("✅ Default recommendation models created")
    
    async def _load_recommendation_data(self):
        """تحميل بيانات التوصيات"""
        try:
            # Load user preferences
            cached_preferences = await self.redis_client.get("user_preferences")
            if cached_preferences:
                self.user_preferences_cache = json.loads(cached_preferences)
            else:
                await self._refresh_user_preferences()
            
            # Load popular items
            cached_popular = await self.redis_client.get("popular_items")
            if cached_popular:
                self.popular_items_cache = json.loads(cached_popular)
            else:
                await self._refresh_popular_items()
                
        except Exception as e:
            logger.error(f"❌ Failed to load recommendation data: {e}")
    
    async def generate_recommendations(
        self,
        user_id: str,
        context: Dict[str, Any] = None,
        count: int = None
    ) -> List[Recommendation]:
        """توليد التوصيات للمستخدم"""
        try:
            start_time = datetime.now()
            count = count or self.recommendation_count
            
            logger.info(f"🎯 Generating recommendations for user: {user_id}")
            
            # Get user preferences
            user_preferences = await self._get_user_preferences(user_id)
            
            # Get user features
            user_features = await self._get_user_features(user_id)
            
            # Generate recommendations from different approaches
            collaborative_recs = await self._collaborative_filtering_recommendations(
                user_id, user_features, count
            )
            
            content_based_recs = await self._content_based_recommendations(
                user_id, user_preferences, count
            )
            
            deep_learning_recs = await self._deep_learning_recommendations(
                user_id, user_features, count
            )
            
            popularity_recs = await self._popularity_based_recommendations(
                user_id, user_preferences, count
            )
            
            # Combine and rank recommendations
            all_recommendations = (
                collaborative_recs + content_based_recs + 
                deep_learning_recs + popularity_recs
            )
            
            # Remove duplicates and rank
            final_recommendations = await self._rank_and_filter_recommendations(
                all_recommendations, user_preferences, context, count
            )
            
            # Add contextual recommendations
            contextual_recs = await self._generate_contextual_recommendations(
                user_id, context, user_preferences
            )
            
            final_recommendations.extend(contextual_recs)
            
            # Final ranking and limiting
            final_recommendations = await self._final_ranking(
                final_recommendations, count
            )
            
            # Cache recommendations
            await self._cache_recommendations(user_id, final_recommendations)
            
            # Update statistics
            self.total_recommendations += len(final_recommendations)
            
            # Log metrics
            processing_time = (datetime.now() - start_time).total_seconds()
            await self.metrics_collector.record_prediction(
                model_type="recommendations",
                score=len(final_recommendations),
                processing_time=processing_time,
                risk_level="info"
            )
            
            logger.info(f"✅ Generated {len(final_recommendations)} recommendations for user: {user_id}")
            
            return final_recommendations
            
        except Exception as e:
            logger.error(f"❌ Recommendation generation failed: {e}")
            raise
    
    async def _collaborative_filtering_recommendations(
        self,
        user_id: str,
        user_features: np.ndarray,
        count: int
    ) -> List[Recommendation]:
        """توصيات التصفية التعاونية"""
        try:
            recommendations = []
            
            if self.collaborative_model is None or self.user_item_matrix is None:
                return recommendations
            
            # Get user index in matrix
            user_index = await self._get_user_index(user_id)
            if user_index is None:
                return recommendations
            
            # Get user's ratings vector
            user_ratings = self.user_item_matrix[user_index]
            
            # Find similar users
            user_similarities = cosine_similarity([user_ratings], self.user_item_matrix)[0]
            similar_users = np.argsort(user_similarities)[::-1][1:11]  # Top 10 similar users
            
            # Get recommendations from similar users
            for similar_user_idx in similar_users:
                similar_user_ratings = self.user_item_matrix[similar_user_idx]
                
                # Find items liked by similar user but not by current user
                for item_idx, rating in enumerate(similar_user_ratings):
                    if rating > 0.7 and user_ratings[item_idx] == 0:  # High rating, not seen by user
                        item_info = await self._get_item_info(item_idx)
                        if item_info:
                            recommendation = Recommendation(
                                id=f"cf_{user_id}_{item_idx}_{int(datetime.now().timestamp())}",
                                user_id=user_id,
                                type=RecommendationType.PRODUCT,
                                title=item_info.get("title", "منتج موصى به"),
                                description=item_info.get("description", "منتج مناسب لك بناءً على تفضيلات مستخدمين مشابهين"),
                                priority=RecommendationPriority.MEDIUM,
                                confidence_score=float(user_similarities[similar_user_idx]),
                                relevance_score=float(rating),
                                expected_value=float(rating * user_similarities[similar_user_idx]),
                                category=item_info.get("category", "general"),
                                tags=["collaborative_filtering", "similar_users"],
                                action_url=item_info.get("url"),
                                expires_at=datetime.now() + timedelta(days=7),
                                created_at=datetime.now(),
                                metadata={"source": "collaborative_filtering", "similar_user_score": float(user_similarities[similar_user_idx])}
                            )
                            recommendations.append(recommendation)
                            
                            if len(recommendations) >= count:
                                break
                
                if len(recommendations) >= count:
                    break
            
            return recommendations[:count]
            
        except Exception as e:
            logger.error(f"❌ Collaborative filtering failed: {e}")
            return []
    
    async def _content_based_recommendations(
        self,
        user_id: str,
        user_preferences: UserPreferences,
        count: int
    ) -> List[Recommendation]:
        """التوصيات القائمة على المحتوى"""
        try:
            recommendations = []
            
            # Get user's preferred categories
            preferred_categories = user_preferences.preferred_categories
            interests = user_preferences.interests
            
            # Generate recommendations based on preferences
            for category in preferred_categories:
                category_items = await self._get_items_by_category(category)
                
                for item in category_items[:3]:  # Top 3 items per category
                    recommendation = Recommendation(
                        id=f"cb_{user_id}_{item['id']}_{int(datetime.now().timestamp())}",
                        user_id=user_id,
                        type=RecommendationType.PRODUCT,
                        title=item.get("title", "منتج موصى به"),
                        description=f"منتج في فئة {category} المفضلة لديك",
                        priority=RecommendationPriority.MEDIUM,
                        confidence_score=0.8,
                        relevance_score=item.get("rating", 0.7),
                        expected_value=item.get("rating", 0.7) * 0.8,
                        category=category,
                        tags=["content_based", category],
                        action_url=item.get("url"),
                        expires_at=datetime.now() + timedelta(days=5),
                        created_at=datetime.now(),
                        metadata={"source": "content_based", "category": category}
                    )
                    recommendations.append(recommendation)
                    
                    if len(recommendations) >= count:
                        break
                
                if len(recommendations) >= count:
                    break
            
            # Add interest-based recommendations
            for interest in interests:
                interest_items = await self._get_items_by_interest(interest)
                
                for item in interest_items[:2]:  # Top 2 items per interest
                    recommendation = Recommendation(
                        id=f"cb_int_{user_id}_{item['id']}_{int(datetime.now().timestamp())}",
                        user_id=user_id,
                        type=RecommendationType.SERVICE,
                        title=item.get("title", "خدمة موصى بها"),
                        description=f"خدمة تتعلق باهتمامك في {interest}",
                        priority=RecommendationPriority.LOW,
                        confidence_score=0.7,
                        relevance_score=item.get("rating", 0.6),
                        expected_value=item.get("rating", 0.6) * 0.7,
                        category=interest,
                        tags=["content_based", "interest", interest],
                        action_url=item.get("url"),
                        expires_at=datetime.now() + timedelta(days=3),
                        created_at=datetime.now(),
                        metadata={"source": "content_based", "interest": interest}
                    )
                    recommendations.append(recommendation)
                    
                    if len(recommendations) >= count:
                        break
                
                if len(recommendations) >= count:
                    break
            
            return recommendations[:count]
            
        except Exception as e:
            logger.error(f"❌ Content-based recommendations failed: {e}")
            return []
    
    async def _deep_learning_recommendations(
        self,
        user_id: str,
        user_features: np.ndarray,
        count: int
    ) -> List[Recommendation]:
        """التوصيات باستخدام التعلم العميق"""
        try:
            recommendations = []
            
            if self.deep_learning_model is None:
                return recommendations
            
            # Get predictions from deep learning model
            predictions = self.deep_learning_model.predict(user_features.reshape(1, -1), verbose=0)[0]
            
            # Get top categories
            top_categories_idx = np.argsort(predictions)[::-1][:5]
            
            category_names = [
                "financial_products", "investment_options", "insurance",
                "loans", "savings", "cards", "transfers", "payments",
                "security_features", "premium_services"
            ]
            
            for i, category_idx in enumerate(top_categories_idx):
                if category_idx < len(category_names):
                    category = category_names[category_idx]
                    confidence = float(predictions[category_idx])
                    
                    if confidence > self.confidence_threshold:
                        recommendation = Recommendation(
                            id=f"dl_{user_id}_{category}_{int(datetime.now().timestamp())}",
                            user_id=user_id,
                            type=RecommendationType.PRODUCT,
                            title=f"منتج {category} موصى به",
                            description=f"منتج في فئة {category} مناسب لملفك الشخصي",
                            priority=RecommendationPriority.HIGH if i < 2 else RecommendationPriority.MEDIUM,
                            confidence_score=confidence,
                            relevance_score=confidence,
                            expected_value=confidence * 0.9,
                            category=category,
                            tags=["deep_learning", "ai_powered"],
                            action_url=f"/products/{category}",
                            expires_at=datetime.now() + timedelta(days=10),
                            created_at=datetime.now(),
                            metadata={"source": "deep_learning", "model_confidence": confidence}
                        )
                        recommendations.append(recommendation)
                        
                        if len(recommendations) >= count:
                            break
            
            return recommendations[:count]
            
        except Exception as e:
            logger.error(f"❌ Deep learning recommendations failed: {e}")
            return []
    
    async def _popularity_based_recommendations(
        self,
        user_id: str,
        user_preferences: UserPreferences,
        count: int
    ) -> List[Recommendation]:
        """التوصيات القائمة على الشعبية"""
        try:
            recommendations = []
            
            # Get popular items
            popular_items = self.popular_items_cache[:count * 2]  # Get more to filter
            
            for item in popular_items:
                # Check if item matches user preferences
                if self._matches_user_preferences(item, user_preferences):
                    recommendation = Recommendation(
                        id=f"pop_{user_id}_{item['id']}_{int(datetime.now().timestamp())}",
                        user_id=user_id,
                        type=RecommendationType.PRODUCT,
                        title=item.get("title", "منتج شائع"),
                        description=f"منتج شائع بين المستخدمين - {item.get('popularity_score', 0):.1f} نقطة شعبية",
                        priority=RecommendationPriority.LOW,
                        confidence_score=0.6,
                        relevance_score=item.get("popularity_score", 0.5),
                        expected_value=item.get("popularity_score", 0.5) * 0.6,
                        category=item.get("category", "general"),
                        tags=["popularity_based", "trending"],
                        action_url=item.get("url"),
                        expires_at=datetime.now() + timedelta(days=2),
                        created_at=datetime.now(),
                        metadata={"source": "popularity", "popularity_score": item.get("popularity_score", 0)}
                    )
                    recommendations.append(recommendation)
                    
                    if len(recommendations) >= count:
                        break
            
            return recommendations[:count]
            
        except Exception as e:
            logger.error(f"❌ Popularity-based recommendations failed: {e}")
            return []
    
    async def _generate_contextual_recommendations(
        self,
        user_id: str,
        context: Dict[str, Any],
        user_preferences: UserPreferences
    ) -> List[Recommendation]:
        """توليد التوصيات السياقية"""
        try:
            recommendations = []
            
            if not context:
                return recommendations
            
            # Time-based recommendations
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 17:  # Business hours
                recommendations.append(Recommendation(
                    id=f"ctx_business_{user_id}_{int(datetime.now().timestamp())}",
                    user_id=user_id,
                    type=RecommendationType.SERVICE,
                    title="خدمات الأعمال",
                    description="خدمات مصرفية للأعمال متاحة الآن",
                    priority=RecommendationPriority.MEDIUM,
                    confidence_score=0.7,
                    relevance_score=0.8,
                    expected_value=0.56,
                    category="business",
                    tags=["contextual", "business_hours"],
                    action_url="/business-services",
                    expires_at=datetime.now() + timedelta(hours=8),
                    created_at=datetime.now(),
                    metadata={"source": "contextual", "context": "business_hours"}
                ))
            
            # Location-based recommendations
            if "location" in context:
                location = context["location"]
                if location.get("country") == "SA":
                    recommendations.append(Recommendation(
                        id=f"ctx_local_{user_id}_{int(datetime.now().timestamp())}",
                        user_id=user_id,
                        type=RecommendationType.PROMOTION,
                        title="عروض محلية",
                        description="عروض خاصة للمقيمين في المملكة العربية السعودية",
                        priority=RecommendationPriority.HIGH,
                        confidence_score=0.9,
                        relevance_score=0.8,
                        expected_value=0.72,
                        category="local_offers",
                        tags=["contextual", "location", "saudi"],
                        action_url="/local-offers",
                        expires_at=datetime.now() + timedelta(days=1),
                        created_at=datetime.now(),
                        metadata={"source": "contextual", "context": "location", "country": "SA"}
                    ))
            
            # Device-based recommendations
            if "device_type" in context:
                if context["device_type"] == "mobile":
                    recommendations.append(Recommendation(
                        id=f"ctx_mobile_{user_id}_{int(datetime.now().timestamp())}",
                        user_id=user_id,
                        type=RecommendationType.FEATURE,
                        title="ميزات الجوال",
                        description="استفد من ميزات التطبيق المحمول الحصرية",
                        priority=RecommendationPriority.LOW,
                        confidence_score=0.6,
                        relevance_score=0.7,
                        expected_value=0.42,
                        category="mobile_features",
                        tags=["contextual", "mobile", "features"],
                        action_url="/mobile-features",
                        expires_at=datetime.now() + timedelta(days=3),
                        created_at=datetime.now(),
                        metadata={"source": "contextual", "context": "mobile_device"}
                    ))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"❌ Contextual recommendations failed: {e}")
            return []
    
    def _matches_user_preferences(self, item: Dict[str, Any], user_preferences: UserPreferences) -> bool:
        """فحص مطابقة العنصر لتفضيلات المستخدم"""
        try:
            # Check category preference
            item_category = item.get("category", "")
            if item_category in user_preferences.preferred_categories:
                return True
            
            # Check interests
            item_tags = item.get("tags", [])
            for interest in user_preferences.interests:
                if interest in item_tags:
                    return True
            
            # Check risk tolerance for financial products
            if item_category in ["investment", "trading", "crypto"]:
                item_risk = item.get("risk_level", "medium")
                if item_risk == user_preferences.risk_tolerance:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Preference matching failed: {e}")
            return False
    
    async def _rank_and_filter_recommendations(
        self,
        recommendations: List[Recommendation],
        user_preferences: UserPreferences,
        context: Dict[str, Any],
        count: int
    ) -> List[Recommendation]:
        """ترتيب وتصفية التوصيات"""
        try:
            # Remove duplicates based on title and category
            seen = set()
            unique_recommendations = []
            
            for rec in recommendations:
                key = (rec.title, rec.category)
                if key not in seen:
                    seen.add(key)
                    unique_recommendations.append(rec)
            
            # Calculate final scores
            for rec in unique_recommendations:
                # Combine confidence and relevance
                base_score = (rec.confidence_score * 0.6 + rec.relevance_score * 0.4)
                
                # Apply priority multiplier
                priority_multiplier = {
                    RecommendationPriority.URGENT: 1.5,
                    RecommendationPriority.HIGH: 1.2,
                    RecommendationPriority.MEDIUM: 1.0,
                    RecommendationPriority.LOW: 0.8
                }
                
                final_score = base_score * priority_multiplier[rec.priority]
                rec.expected_value = final_score
            
            # Sort by expected value
            unique_recommendations.sort(key=lambda x: x.expected_value, reverse=True)
            
            return unique_recommendations[:count]
            
        except Exception as e:
            logger.error(f"❌ Recommendation ranking failed: {e}")
            return recommendations[:count]
    
    async def _final_ranking(self, recommendations: List[Recommendation], count: int) -> List[Recommendation]:
        """الترتيب النهائي للتوصيات"""
        try:
            # Apply diversity to avoid too many recommendations from same category
            final_recommendations = []
            category_count = {}
            
            for rec in recommendations:
                category = rec.category
                current_count = category_count.get(category, 0)
                
                # Limit recommendations per category
                if current_count < 3:  # Max 3 per category
                    final_recommendations.append(rec)
                    category_count[category] = current_count + 1
                    
                    if len(final_recommendations) >= count:
                        break
            
            return final_recommendations
            
        except Exception as e:
            logger.error(f"❌ Final ranking failed: {e}")
            return recommendations[:count]
    
    async def _get_user_preferences(self, user_id: str) -> UserPreferences:
        """الحصول على تفضيلات المستخدم"""
        if user_id in self.user_preferences_cache:
            return self.user_preferences_cache[user_id]
        
        # Load from database
        preferences_data = await self.db_manager.get_user_preferences(user_id)
        
        if preferences_data:
            preferences = UserPreferences(**preferences_data)
            self.user_preferences_cache[user_id] = preferences
            return preferences
        
        # Return default preferences
        return UserPreferences(
            user_id=user_id,
            preferred_categories=["financial_services", "transfers"],
            preferred_languages=["ar"],
            preferred_channels=["mobile", "web"],
            risk_tolerance="medium",
            investment_goals=["savings", "growth"],
            communication_frequency="weekly",
            interests=["finance", "technology"],
            demographics={"age_group": "25-35", "country": "SA"}
        )
    
    async def _get_user_features(self, user_id: str) -> np.ndarray:
        """الحصول على ميزات المستخدم"""
        try:
            # Get user profile and transaction history
            user_profile = await self.db_manager.get_user_profile(user_id)
            transaction_history = await self.db_manager.get_user_transactions(user_id, limit=100)
            
            # Extract features
            features = await self.feature_engineer.extract_user_recommendation_features(
                user_profile, transaction_history
            )
            
            # Convert to numpy array
            feature_array = np.array(list(features.values()))
            
            # Pad or truncate to fixed size (100 features)
            if len(feature_array) < 100:
                feature_array = np.pad(feature_array, (0, 100 - len(feature_array)))
            else:
                feature_array = feature_array[:100]
            
            return feature_array
            
        except Exception as e:
            logger.error(f"❌ User feature extraction failed: {e}")
            return np.zeros(100)  # Return default features
    
    async def _build_user_item_matrix(self):
        """بناء مصفوفة المستخدم-العنصر"""
        try:
            # This would typically load interaction data from database
            # For now, we'll create a dummy matrix
            n_users = 1000
            n_items = 500
            
            # Create sparse matrix with some random interactions
            self.user_item_matrix = np.random.rand(n_users, n_items)
            self.user_item_matrix[self.user_item_matrix < 0.9] = 0  # Make it sparse
            
            logger.info(f"✅ User-item matrix built: {n_users} users x {n_items} items")
            
        except Exception as e:
            logger.error(f"❌ Failed to build user-item matrix: {e}")
    
    async def _perform_user_clustering(self):
        """تجميع المستخدمين"""
        try:
            if self.clustering_model is None or self.user_item_matrix is None:
                return
            
            # Perform clustering on user features
            user_features = self.user_item_matrix
            clusters = self.clustering_model.fit_predict(user_features)
            
            # Store cluster assignments
            for user_idx, cluster in enumerate(clusters):
                self.user_clusters[user_idx] = cluster
            
            logger.info(f"✅ User clustering completed: {len(set(clusters))} clusters")
            
        except Exception as e:
            logger.error(f"❌ User clustering failed: {e}")
    
    async def _cache_recommendations(self, user_id: str, recommendations: List[Recommendation]):
        """حفظ التوصيات في الكاش"""
        try:
            cache_key = f"recommendations:{user_id}"
            cache_data = [
                {
                    "id": rec.id,
                    "type": rec.type.value,
                    "title": rec.title,
                    "description": rec.description,
                    "priority": rec.priority.value,
                    "confidence_score": rec.confidence_score,
                    "relevance_score": rec.relevance_score,
                    "expected_value": rec.expected_value,
                    "category": rec.category,
                    "tags": rec.tags,
                    "action_url": rec.action_url,
                    "created_at": rec.created_at.isoformat(),
                    "metadata": rec.metadata
                }
                for rec in recommendations
            ]
            
            await self.redis_client.setex(
                cache_key,
                3600,  # 1 hour TTL
                json.dumps(cache_data)
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to cache recommendations: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات محرك التوصيات"""
        return {
            "total_recommendations": self.total_recommendations,
            "successful_recommendations": self.successful_recommendations,
            "success_rate": self.successful_recommendations / max(self.total_recommendations, 1),
            "model_version": self.model_version,
            "confidence_threshold": self.confidence_threshold,
            "cache_size": {
                "user_preferences": len(self.user_preferences_cache),
                "recommendations": len(self.recommendations_cache),
                "popular_items": len(self.popular_items_cache)
            },
            "user_clusters": len(set(self.user_clusters.values())) if self.user_clusters else 0
        }
    
    async def cleanup(self):
        """تنظيف الموارد"""
        try:
            logger.info("🧹 Cleaning up Recommendation Engine...")
            
            # Clear caches
            self.user_preferences_cache.clear()
            self.recommendations_cache.clear()
            self.popular_items_cache.clear()
            self.user_clusters.clear()
            
            # Cleanup models
            if self.deep_learning_model:
                del self.deep_learning_model
            
            logger.info("✅ Recommendation Engine cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Recommendation Engine cleanup failed: {e}")
    
    # Helper methods (simplified implementations)
    async def _get_user_index(self, user_id: str) -> Optional[int]:
        """الحصول على فهرس المستخدم في المصفوفة"""
        # This would map user_id to matrix index
        return hash(user_id) % 1000 if self.user_item_matrix is not None else None
    
    async def _get_item_info(self, item_idx: int) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات العنصر"""
        # This would fetch item details from database
        return {
            "title": f"منتج {item_idx}",
            "description": f"وصف المنتج {item_idx}",
            "category": "general",
            "url": f"/products/{item_idx}",
            "rating": 0.8
        }
    
    async def _get_items_by_category(self, category: str) -> List[Dict[str, Any]]:
        """الحصول على العناصر حسب الفئة"""
        # This would query database for items in category
        return [
            {"id": f"{category}_{i}", "title": f"منتج {category} {i}", "rating": 0.8, "url": f"/products/{category}/{i}"}
            for i in range(1, 6)
        ]
    
    async def _get_items_by_interest(self, interest: str) -> List[Dict[str, Any]]:
        """الحصول على العناصر حسب الاهتمام"""
        # This would query database for items related to interest
        return [
            {"id": f"{interest}_{i}", "title": f"خدمة {interest} {i}", "rating": 0.7, "url": f"/services/{interest}/{i}"}
            for i in range(1, 4)
        ]
    
    async def _refresh_user_preferences(self):
        """تحديث تفضيلات المستخدمين"""
        try:
            # This would load user preferences from database
            pass
        except Exception as e:
            logger.error(f"❌ Failed to refresh user preferences: {e}")
    
    async def _refresh_popular_items(self):
        """تحديث العناصر الشائعة"""
        try:
            # This would load popular items from database
            self.popular_items_cache = [
                {"id": f"popular_{i}", "title": f"منتج شائع {i}", "popularity_score": 0.9 - i*0.1, "category": "popular"}
                for i in range(1, 11)
            ]
        except Exception as e:
            logger.error(f"❌ Failed to refresh popular items: {e}")
